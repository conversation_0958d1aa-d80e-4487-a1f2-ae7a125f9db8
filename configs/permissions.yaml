# Discord Bot 权限配置

# Bot 所有者（拥有所有权限）
bot_owners:
  - "1245089093071802470"

# 是否启用权限检查
enable_permission_check: true

# 命令权限要求
command_permissions:
  filter:
    allowed_roles:
      - "1356630035943129322"  # 管理员角色ID（请替换为实际角色ID）
      - "1381660838980616413"  # 频道管理员角色ID（请替换为实际角色ID）
    allowed_users:
      - "1105120359386193970"  # 特定用户ID（请替换为实际用户ID）
    fallback_permissions:
      - "MANAGE_CHANNELS"

  forward:
    allowed_roles:
      - "1384685660354646116"  # 仅管理员角色ID（请替换为实际角色ID）
    allowed_users:
      - "1105120359386193970"  # Bot管理员用户ID（请替换为实际用户ID）
    fallback_permissions:
      - "ADMINISTRATOR"

# 表情反应权限
reaction_permissions:
  allowed_roles:
    - "1356630035943129322"  # 管理员角色ID（请替换为实际角色ID）
    - "1381660838980616413"  # 版主角色ID（请替换为实际角色ID）
  allowed_users:
    - "1105120359386193970"  # 特定用户ID（请替换为实际用户ID）
  fallback_permissions:
    - "MANAGE_MESSAGES"

# 表情反应过滤配置
reaction_filter:
  enabled: true
  whitelist_emoji: "✅"
  blacklist_emoji: "❌"
  admin_users:
    - "1105120359386193970"  # 管理员用户ID（请替换为实际用户ID）
  admin_roles:
    - "1356630035943129322"  # 管理员角色ID（请替换为实际角色ID）
    - "1381660838980616413"  # 版主角色ID（请替换为实际角色ID）

package types

import (
	"fmt"
	"strings"
	"time"
)

// FilterRule 极简的过滤规则结构（最终简化版）
// 使用 Channel + Keyword + Mode 作为唯一标识
type FilterRule struct {
	// 核心字段
	Channel   string `yaml:"channel" json:"channel"`                           // 应用的频道
	Keyword   string `yaml:"keyword,omitempty" json:"keyword,omitempty"`       // 统一关键字（在ProductID, Title, URL中搜索）
	Mode      string `yaml:"mode" json:"mode"`                                 // whitelist, blacklist（从+/-前缀解析得出）
	CreatedBy string `yaml:"created_by,omitempty" json:"created_by,omitempty"` // 创建者用户ID

	// 向后兼容字段（内部使用，不在配置文件中显示）
	ID        string `yaml:"-" json:"id,omitempty"`
	ChannelID string `yaml:"-" json:"channel_id,omitempty"`
	ProductID string `yaml:"-" json:"product_id,omitempty"`
	Enabled   bool   `yaml:"-" json:"enabled,omitempty"` // 向后兼容，内部始终为true
}

// ChannelInfo 频道信息（遵循设计文档）
type ChannelInfo struct {
	ChannelID   string    `yaml:"channel_id" json:"channel_id"`
	ChannelName string    `yaml:"channel_name" json:"channel_name"`
	GuildID     string    `yaml:"guild_id" json:"guild_id"`
	CreatedAt   time.Time `yaml:"created_at" json:"created_at"`
	UpdatedAt   time.Time `yaml:"updated_at" json:"updated_at"`
}

// FilterRulesConfig 过滤规则配置文件结构（遵循设计文档）
type FilterRulesConfig struct {
	ChannelInfo ChannelInfo  `yaml:"channel_info" json:"channel_info"`
	FilterRules []FilterRule `yaml:"filter_rules" json:"filter_rules"`
}

// FilterResult 过滤结果
type FilterResult struct {
	Allowed       bool                   `json:"allowed"`
	Action        string                 `json:"action"`
	MatchedRules  []string               `json:"matched_rules"`
	Reason        string                 `json:"reason,omitempty"`
	ModifiedData  map[string]interface{} `json:"modified_data,omitempty"`
	CheckTime     time.Time              `json:"check_time"`
	ProcessTimeMs float64                `json:"process_time_ms"`
}

// GlobalFilterSettings 全局过滤设置
type GlobalFilterSettings struct {
	DefaultAction        string        `yaml:"default_action" json:"default_action"`
	DefaultLogicalOp     string        `yaml:"default_logical_op" json:"default_logical_op"`
	CaseSensitiveDefault bool          `yaml:"case_sensitive_default" json:"case_sensitive_default"`
	MaxRulesPerChannel   int           `yaml:"max_rules_per_channel" json:"max_rules_per_channel"`
	CacheEnabled         bool          `yaml:"cache_enabled" json:"cache_enabled"`
	CacheTTL             time.Duration `yaml:"cache_ttl" json:"cache_ttl"`
}

// ChannelFilterStats 频道过滤统计
type ChannelFilterStats struct {
	ChannelID        string    `json:"channel_id"`
	TotalMessages    int64     `json:"total_messages"`
	AllowedMessages  int64     `json:"allowed_messages"`
	DeniedMessages   int64     `json:"denied_messages"`
	LastProcessTime  time.Time `json:"last_process_time"`
	AverageProcessMs float64   `json:"average_process_ms"`
}

// Validate 验证过滤规则（遵循设计文档的极简结构）
func (fr *FilterRule) Validate() error {
	if fr.Channel == "" {
		return fmt.Errorf("频道不能为空")
	}

	// 验证模式
	validModes := []string{"whitelist", "blacklist"}
	if fr.Mode == "" {
		fr.Mode = "blacklist" // 默认黑名单
	} else {
		found := false
		for _, mode := range validModes {
			if fr.Mode == mode {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("无效的过滤模式: %s，支持的模式: %v", fr.Mode, validModes)
		}
	}

	// 移除来源验证，统一处理所有规则

	// 内部字段设置（向后兼容）
	fr.Enabled = true // 添加的规则始终启用

	return nil
}

// GetUniqueID 获取唯一标识符（Channel + Keyword + Mode）
func (fr *FilterRule) GetUniqueID() string {
	return fmt.Sprintf("%s_%s_%s", fr.Channel, fr.Keyword, fr.Mode)
}

// MatchesContent 检查内容是否匹配过滤规则
func (fr *FilterRule) MatchesContent(content string) bool {
	if fr.Keyword == "" {
		return true // 没有关键词，默认匹配
	}

	// 统一关键字搜索（在ProductID, Title, URL中搜索）
	contentLower := strings.ToLower(content)
	keywordLower := strings.ToLower(fr.Keyword)

	matches := strings.Contains(contentLower, keywordLower)

	// 根据模式返回结果
	if fr.Mode == "whitelist" {
		return matches // 白名单：匹配则允许
	} else {
		return matches // 黑名单：匹配则拒绝
	}
}

// ParseKeywordWithMode 从带前缀的关键词解析模式（+/-前缀）
func ParseKeywordWithMode(keywordWithPrefix string) (keyword, mode string) {
	if strings.HasPrefix(keywordWithPrefix, "+") {
		return strings.TrimPrefix(keywordWithPrefix, "+"), "whitelist"
	} else if strings.HasPrefix(keywordWithPrefix, "-") {
		return strings.TrimPrefix(keywordWithPrefix, "-"), "blacklist"
	} else {
		return keywordWithPrefix, "blacklist" // 默认黑名单
	}
}

// FormatKeywordWithMode 格式化关键词为带前缀的形式
func (fr *FilterRule) FormatKeywordWithMode() string {
	if fr.Mode == "whitelist" {
		return "+" + fr.Keyword
	} else {
		return "-" + fr.Keyword
	}
}

// GenerateFilterRuleID 生成过滤规则ID（向后兼容）
func GenerateFilterRuleID(channelID, productID string) string {
	if productID != "" {
		return fmt.Sprintf("filter_%s_%s_%d", channelID, productID, time.Now().Unix())
	}
	return fmt.Sprintf("filter_%s_%d", channelID, time.Now().Unix())
}

// FilterEngine 过滤引擎接口
type FilterEngine interface {
	// 规则管理
	AddRule(rule *FilterRule) error
	RemoveRule(ruleID string) error
	UpdateRule(rule *FilterRule) error
	GetRule(ruleID string) (*FilterRule, error)
	ListRules(channelID string) []*FilterRule

	// 过滤检查
	CheckMessage(channelID string, message interface{}) (*FilterResult, error)
	CheckProduct(channelID string, product *ProductItem) (*FilterResult, error)
}

// 向后兼容的类型别名
type (
	// 保持旧的复杂结构作为内部使用（不导出到配置文件）
	LegacyFilterRule       = FilterRule
	LegacyFilterConditions struct {
		Keywords      []string `yaml:"keywords" json:"keywords"`
		KeywordMode   string   `yaml:"keyword_mode" json:"keyword_mode"`
		CaseSensitive bool     `yaml:"case_sensitive" json:"case_sensitive"`
	}
	LegacyFilterAction struct {
		Type        string `yaml:"type" json:"type"`
		Description string `yaml:"description" json:"description"`
	}
)

// ConvertFromLegacy 从旧格式转换为新格式
func ConvertFromLegacy(legacy *LegacyFilterRule) []*FilterRule {
	var rules []*FilterRule

	// 如果有旧的复杂结构，转换为多个简单规则
	if legacy.ChannelID != "" {
		// 基础规则
		rule := &FilterRule{
			Channel: legacy.ChannelID,
			Mode:    "blacklist",
		}

		// 设置向后兼容字段
		rule.ID = legacy.ID
		rule.ChannelID = legacy.ChannelID
		rule.ProductID = legacy.ProductID

		rules = append(rules, rule)
	}

	return rules
}

// ConvertToLegacy 转换为旧格式（向后兼容）
func (fr *FilterRule) ConvertToLegacy() *LegacyFilterRule {
	legacy := &LegacyFilterRule{
		Channel:   fr.Channel,
		Keyword:   fr.Keyword,
		Mode:      fr.Mode,
		CreatedBy: fr.CreatedBy,
		ID:        fr.ID,
		ChannelID: fr.ChannelID,
		ProductID: fr.ProductID,
		Enabled:   true, // 始终启用
	}

	return legacy
}

package bot

import (
	"context"
	"fmt"
	"sync"
	"time"

	"zeka-go/internal/events"
	"zeka-go/internal/handlers"
	"zeka-go/internal/services"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/services/notification"
	"zeka-go/internal/tasks"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// 上下文键类型
type contextKey string

const (
	contextKeyUserID    contextKey = "user_id"
	contextKeyGuildID   contextKey = "guild_id"
	contextKeyChannelID contextKey = "channel_id"
)

// Bot Discord Bot 实现 - 简化为协调器角色
type Bot struct {
	config  *types.Config
	session *discordgo.Session
	client  *types.Client

	// 核心管理器
	serviceManager  *services.ServiceManager
	handlerRegistry *handlers.HandlerRegistry
	commandManager  *handlers.CommandManager
	eventManager    *handlers.EventManager
	taskLoader      *tasks.TaskLoader

	// 状态管理
	isRunning bool
	mu        sync.RWMutex

	// 上下文管理
	ctx    context.Context
	cancel context.CancelFunc
}

// New 创建新的 Bot 实例
func New(config *types.Config) (types.Bot, error) {
	if config == nil {
		return nil, types.ErrInvalidConfig
	}

	// 创建 Discord 会话
	session, err := discordgo.New("Bot " + config.Discord.Token)
	if err != nil {
		return nil, types.NewServiceError("discord", "create_session", "创建 Discord 会话失败", err)
	}

	// 设置 Intent
	session.Identify.Intents = discordgo.IntentsGuilds |
		discordgo.IntentsGuildMessages |
		discordgo.IntentsGuildMembers |
		discordgo.IntentsMessageContent |
		discordgo.IntentsGuildVoiceStates |
		discordgo.IntentsGuildInvites |
		discordgo.IntentsGuildMessageReactions

	// 创建扩展客户端
	client := types.NewClient(session, config)

	bot := &Bot{
		config:  config,
		session: session,
		client:  client,
	}

	// 初始化服务管理器
	bot.serviceManager = services.NewServiceManager(config)

	// 初始化处理器注册表
	bot.handlerRegistry = handlers.NewHandlerRegistry()

	// 初始化命令管理器
	bot.commandManager = handlers.NewCommandManager(session, config)

	// 设置服务管理器到命令管理器
	bot.commandManager.SetServiceManager(bot.serviceManager)

	// 使用初始化器进行复杂的初始化
	if err := bot.initializeWithInitializer(); err != nil {
		return nil, fmt.Errorf("初始化失败: %w", err)
	}

	// 设置事件处理器
	bot.setupEventHandlers()

	// 设置事件监听器
	// TODO: 重构后需要使用HandlerRegistry来设置事件监听器
	// bot.eventManager.SetupEventListeners(bot.client)

	return bot, nil
}

// initializeWithInitializer 使用初始化器进行复杂的初始化
func (b *Bot) initializeWithInitializer() error {
	logger.Info("正在使用初始化器进行初始化...")

	// 创建初始化器
	initializer := NewBotInitializer(b.config, b.serviceManager, b.client)

	// 注册所有服务
	if err := initializer.RegisterAllServices(); err != nil {
		return fmt.Errorf("注册服务失败: %w", err)
	}

	// 注册处理器
	if err := b.registerHandlers(); err != nil {
		return fmt.Errorf("注册处理器失败: %w", err)
	}

	// 初始化服务
	if err := b.initializeServices(initializer); err != nil {
		return fmt.Errorf("初始化服务失败: %w", err)
	}

	logger.Info("✅ 初始化器初始化完成")
	return nil
}

// initializeServices 初始化服务（简化版）
func (b *Bot) initializeServices(initializer *BotInitializer) error {
	logger.Info("正在初始化服务...")

	// 使用ServiceManager初始化所有服务
	ctx := context.Background()
	if err := b.serviceManager.InitializeServices(ctx); err != nil {
		return fmt.Errorf("服务初始化失败: %w", err)
	}

	// 使用初始化器注入服务依赖
	if err := initializer.InjectAllDependencies(); err != nil {
		return fmt.Errorf("注入服务依赖失败: %w", err)
	}

	logger.Info("✅ 所有服务初始化完成")
	return nil
}

// verifyServiceConnections 验证服务连接状态 - 使用ServiceManager
func (b *Bot) verifyServiceConnections(ctx context.Context) error {
	logger.Info("开始验证服务连接状态...")

	// 使用ServiceManager执行健康检查
	results := b.serviceManager.PerformHealthCheck(ctx)

	hasErrors := false
	for serviceName, result := range results {
		if result.Healthy {
			logger.Info("✅ 服务健康检查通过", "service", serviceName)
		} else {
			logger.Error("❌ 服务健康检查失败", "service", serviceName, "error", result.Message)
			hasErrors = true
		}
	}

	if hasErrors {
		logger.Warn("部分服务健康检查失败，但继续启动")
	}

	logger.Info("✅ 所有服务连接验证完成")
	return nil
}

// setupEventHandlers 设置事件处理器
func (b *Bot) setupEventHandlers() {
	// Ready 事件
	b.session.AddHandler(func(s *discordgo.Session, r *discordgo.Ready) {
		logger.Info("Bot 已连接到 Discord",
			"user", r.User.Username+"#"+r.User.Discriminator,
			"guilds", len(r.Guilds))

		// 设置状态
		b.setStatus()
	})

	// 交互事件
	b.session.AddHandler(func(s *discordgo.Session, i *discordgo.InteractionCreate) {
		b.handleInteraction(s, i)
	})

	// 消息事件
	b.session.AddHandler(func(s *discordgo.Session, m *discordgo.MessageCreate) {
		b.handleMessage(s, m)
	})

	// 服务器加入事件
	b.session.AddHandler(func(s *discordgo.Session, g *discordgo.GuildCreate) {
		logger.Info("加入服务器", "guild", g.Name, "id", g.ID, "members", g.MemberCount)
	})

	// 服务器离开事件
	b.session.AddHandler(func(s *discordgo.Session, g *discordgo.GuildDelete) {
		logger.Info("离开服务器", "guild", g.Name, "id", g.ID)
	})
}

// setStatus 设置 Bot 状态
func (b *Bot) setStatus() {
	if len(b.config.StatusMessages) == 0 {
		return
	}

	// 使用第一个状态消息
	status := b.config.StatusMessages[0]

	var activityType discordgo.ActivityType
	switch status.Type {
	case "Playing":
		activityType = discordgo.ActivityTypeGame
	case "Listening":
		activityType = discordgo.ActivityTypeListening
	case "Watching":
		activityType = discordgo.ActivityTypeWatching
	case "Competing":
		activityType = discordgo.ActivityTypeCompeting
	default:
		activityType = discordgo.ActivityTypeGame
	}

	err := b.session.UpdateGameStatus(0, status.Content)
	if err != nil {
		logger.Error("设置状态失败", "error", err)
		return
	}

	// 设置活动
	err = b.session.UpdateStatusComplex(discordgo.UpdateStatusData{
		Activities: []*discordgo.Activity{
			{
				Name: status.Content,
				Type: activityType,
			},
		},
		Status: "online",
	})
	if err != nil {
		logger.Error("设置活动状态失败", "error", err)
	}
}

// handleInteraction 处理交互事件
func (b *Bot) handleInteraction(s *discordgo.Session, i *discordgo.InteractionCreate) {
	ctx := context.WithValue(b.ctx, contextKeyUserID, i.Member.User.ID)
	ctx = context.WithValue(ctx, contextKeyGuildID, i.GuildID)
	ctx = context.WithValue(ctx, contextKeyChannelID, i.ChannelID)

	switch i.Type {
	case discordgo.InteractionApplicationCommand:
		b.handleSlashCommand(ctx, s, i)
	case discordgo.InteractionMessageComponent:
		b.handleComponent(ctx, s, i)
	case discordgo.InteractionModalSubmit:
		b.handleModal(ctx, s, i)
	}
}

// handleSlashCommand 处理斜杠命令
func (b *Bot) handleSlashCommand(ctx context.Context, s *discordgo.Session, i *discordgo.InteractionCreate) {
	commandName := i.ApplicationCommandData().Name

	// 获取用户ID
	var userID string
	if i.Member != nil && i.Member.User != nil {
		userID = i.Member.User.ID
	} else if i.User != nil {
		userID = i.User.ID
	} else {
		userID = "unknown"
	}

	// 检查冷却时间
	if b.isOnCooldown(commandName, userID) {
		b.respondWithError(s, i, "命令冷却中，请稍后再试")
		return
	}

	// 使用CommandManager处理命令
	if err := b.commandManager.HandleSlashCommand(ctx, b.client, i); err != nil {
		logger.Error("命令执行失败",
			"command", commandName,
			"user", userID,
			"error", err)
		b.respondWithError(s, i, "命令执行失败: "+err.Error())
		return
	}

	// 设置冷却时间
	b.setCooldown(commandName, userID)
}

// handleComponent 处理组件交互
func (b *Bot) handleComponent(ctx context.Context, s *discordgo.Session, i *discordgo.InteractionCreate) {
	customID := i.MessageComponentData().CustomID

	// 获取用户ID
	var userID string
	if i.Member != nil && i.Member.User != nil {
		userID = i.Member.User.ID
	} else if i.User != nil {
		userID = i.User.ID
	} else {
		userID = "unknown"
	}

	// 使用HandlerRegistry处理组件交互
	if err := b.handlerRegistry.ProcessComponent(ctx, b.client, i); err != nil {
		logger.Error("组件处理失败",
			"custom_id", customID,
			"user", userID,
			"error", err)
		b.respondWithError(s, i, "组件处理失败: "+err.Error())
	}
}

// handleModal 处理模态框
func (b *Bot) handleModal(ctx context.Context, s *discordgo.Session, i *discordgo.InteractionCreate) {
	// 使用HandlerRegistry处理模态框（模态框也是组件的一种）
	if err := b.handlerRegistry.ProcessComponent(ctx, b.client, i); err != nil {
		customID := i.ModalSubmitData().CustomID
		logger.Error("模态框处理失败",
			"custom_id", customID,
			"user", i.Member.User.ID,
			"error", err)
		b.respondWithError(s, i, "模态框处理失败")
	}
}

// handleMessage 处理消息事件
func (b *Bot) handleMessage(s *discordgo.Session, m *discordgo.MessageCreate) {
	// 忽略机器人消息
	if m.Author.Bot {
		return
	}

	// TODO: 处理前缀命令（如果需要）
}

// isOnCooldown 检查是否在冷却时间内
func (b *Bot) isOnCooldown(command, userID string) bool {
	return b.client.CooldownManager.IsOnCooldown(userID, command)
}

// setCooldown 设置冷却时间
func (b *Bot) setCooldown(command, userID string) {
	// 获取冷却时间配置
	cooldown := b.config.Cooldowns.Default
	if cmdCooldown, exists := b.config.Cooldowns.Commands[command]; exists {
		cooldown = cmdCooldown
	}

	b.client.CooldownManager.SetCooldown(userID, command, cooldown)
}

// respondWithError 响应错误消息
func (b *Bot) respondWithError(s *discordgo.Session, i *discordgo.InteractionCreate, message string) {
	err := s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Content: "❌ " + message,
			Flags:   discordgo.MessageFlagsEphemeral,
		},
	})
	if err != nil {
		logger.Error("响应交互失败", "error", err)
	}
}

// Start 启动 Bot
func (b *Bot) Start(ctx context.Context) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if b.isRunning {
		return types.ErrBotAlreadyStarted
	}

	// 设置上下文
	b.ctx, b.cancel = context.WithCancel(ctx)

	// 连接到 Discord
	if err := b.session.Open(); err != nil {
		return fmt.Errorf("连接 Discord 失败: %w", err)
	}

	// 等待连接建立
	time.Sleep(2 * time.Second)

	// 注册默认命令
	if err := b.commandManager.RegisterDefaultCommands(); err != nil {
		logger.Error("注册默认命令失败", "error", err)
		// 不返回错误，继续启动
	}

	// 同步应用命令到 Discord
	if err := b.commandManager.SyncApplicationCommands(); err != nil {
		logger.Error("同步应用命令失败", "error", err)
		// 不返回错误，继续启动
	}

	// 启动服务管理器中的所有服务（先启动服务）
	if err := b.serviceManager.StartServices(b.ctx); err != nil {
		return fmt.Errorf("启动服务失败: %w", err)
	}
	logger.Info("✅ 所有服务已启动")

	// 验证服务连接状态（在服务启动后）
	if err := b.verifyServiceConnections(ctx); err != nil {
		logger.Error("服务连接验证失败", "error", err)
		return fmt.Errorf("服务连接验证失败: %w", err)
	}

	// 初始化任务加载器（在服务启动后）
	if err := b.initializeTaskLoader(); err != nil {
		logger.Error("初始化任务加载器失败", "error", err)
		if b.config.Queue.EnableGracefulDegradation {
			logger.Warn("⚠️ 任务加载器初始化失败，但启用了优雅降级模式，继续启动")
		} else {
			return fmt.Errorf("初始化任务加载器失败: %w", err)
		}
	} else {
		logger.Info("✅ 任务加载器初始化完成")
	}

	b.isRunning = true
	logger.Info("Bot 启动成功")

	return nil
}

// Shutdown 关闭 Bot
func (b *Bot) Shutdown(ctx context.Context) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if !b.isRunning {
		return types.ErrBotNotStarted
	}

	logger.Info("正在关闭 Bot...")

	// 创建带超时的关闭上下文
	shutdownCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	var shutdownErrors []error

	// 取消Bot的上下文
	if b.cancel != nil {
		b.cancel()
	}

	// 优雅关闭任务加载器（先停止接收新任务）
	if b.taskLoader != nil {
		logger.Info("正在关闭任务加载器...")
		if err := b.shutdownWithTimeout(shutdownCtx, "任务加载器", func() error {
			return b.taskLoader.Shutdown()
		}); err != nil {
			shutdownErrors = append(shutdownErrors, fmt.Errorf("关闭任务加载器失败: %w", err))
		}
	}

	// 使用ServiceManager关闭所有服务
	logger.Info("正在关闭所有服务...")
	if err := b.serviceManager.StopServices(shutdownCtx); err != nil {
		shutdownErrors = append(shutdownErrors, fmt.Errorf("关闭服务失败: %w", err))
	} else {
		logger.Info("✅ 所有服务已关闭")
	}

	// 最后关闭 Discord 连接
	logger.Info("正在关闭 Discord 连接...")
	if err := b.session.Close(); err != nil {
		shutdownErrors = append(shutdownErrors, fmt.Errorf("关闭 Discord 连接失败: %w", err))
	}

	b.isRunning = false

	if len(shutdownErrors) > 0 {
		logger.Warn("Bot 关闭过程中出现错误", "errors", len(shutdownErrors))
		for i, err := range shutdownErrors {
			logger.Error("关闭错误", "index", i+1, "error", err)
		}
		return fmt.Errorf("Bot 关闭过程中出现 %d 个错误", len(shutdownErrors))
	}

	logger.Info("Bot 已成功关闭")
	return nil
}

// shutdownWithTimeout 带超时的关闭操作
func (b *Bot) shutdownWithTimeout(ctx context.Context, serviceName string, shutdownFunc func() error) error {
	done := make(chan error, 1)

	go func() {
		done <- shutdownFunc()
	}()

	select {
	case err := <-done:
		if err != nil {
			logger.Error("服务关闭失败", "service", serviceName, "error", err)
		} else {
			logger.Info("服务已关闭", "service", serviceName)
		}
		return err
	case <-ctx.Done():
		logger.Warn("服务关闭超时", "service", serviceName, "timeout", "30s")
		return fmt.Errorf("关闭 %s 超时", serviceName)
	}
}

// GetSession 获取 Discord 会话
func (b *Bot) GetSession() *discordgo.Session {
	return b.session
}

// GetConfig 获取配置
func (b *Bot) GetConfig() *types.Config {
	return b.config
}

// initializeTaskLoader 初始化任务加载器
func (b *Bot) initializeTaskLoader() error {
	logger.Info("正在初始化任务加载器...")

	// 设置客户端服务容器
	b.setupClientServices()

	// 创建任务加载器
	queueService := b.GetQueueService()
	b.taskLoader = tasks.NewTaskLoader(b.client, queueService)

	// 使用初始化器注册任务模块
	initializer := NewBotInitializer(b.config, b.serviceManager, b.client)
	if err := initializer.RegisterTaskModules(b.taskLoader); err != nil {
		return fmt.Errorf("注册任务模块失败: %w", err)
	}

	return nil
}

// setupClientServices 设置客户端服务容器
func (b *Bot) setupClientServices() {
	if b.client.Services == nil {
		b.client.Services = &types.ServiceContainer{}
	}

	// 设置队列服务
	b.client.Services.QueueService = b.GetQueueService()

	// 设置转发服务
	if forwardServiceRaw, err := b.serviceManager.GetService("ForwardRuleService"); err == nil {
		if forwardService, ok := forwardServiceRaw.(types.ForwardRuleManager); ok {
			b.client.Services.ForwardService = forwardService
			logger.Debug("转发服务已设置到客户端服务容器")
		}
	}

	// 设置映射服务
	if mappingServiceRaw, err := b.serviceManager.GetService("FieldMappingService"); err == nil {
		if mappingService, ok := mappingServiceRaw.(types.FieldMapper); ok {
			b.client.Services.MappingService = mappingService
			logger.Debug("映射服务已设置到客户端服务容器")
		} else {
			logger.Warn("映射服务类型断言失败")
		}
	} else {
		logger.Warn("获取映射服务失败", "error", err)
	}

	// 设置产品通知服务
	if productNotificationServiceRaw, err := b.serviceManager.GetService("product_notification"); err == nil {
		if productNotificationAdapter, ok := productNotificationServiceRaw.(*notification.ProductNotificationServiceAdapter); ok {
			if productNotificationService := productNotificationAdapter.GetService(); productNotificationService != nil {
				b.client.Services.ProductNotificationService = productNotificationService
				logger.Debug("产品通知服务已设置到客户端服务容器")
			} else {
				logger.Warn("通知服务适配器返回了 nil 服务实例")
			}
		} else {
			logger.Warn("通知服务类型断言失败")
		}
	} else {
		logger.Warn("获取通知服务失败", "error", err)
	}
}

// registerHandlers 注册处理器到处理器注册表
func (b *Bot) registerHandlers() error {
	// 注册事件处理器
	if err := b.registerEventHandlers(); err != nil {
		return fmt.Errorf("注册事件处理器失败: %w", err)
	}

	logger.Info("处理器注册完成")
	return nil
}

// registerEventHandlers 注册事件处理器
func (b *Bot) registerEventHandlers() error {
	// 导入事件处理器包
	// 注意：这里需要导入 events 包

	// 获取FilterService
	filterServiceRaw, err := b.serviceManager.GetService("FilterRuleService")
	if err != nil {
		return fmt.Errorf("获取FilterService失败: %w", err)
	}

	// 类型断言为FilterEngine接口
	filterService, ok := filterServiceRaw.(types.FilterEngine)
	if !ok {
		return fmt.Errorf("FilterRuleService未实现FilterEngine接口")
	}

	// 获取ForwardRuleService
	forwardServiceRaw, err := b.serviceManager.GetService("ForwardRuleService")
	if err != nil {
		return fmt.Errorf("获取ForwardRuleService失败: %w", err)
	}

	// 类型断言为ForwardRuleManager接口
	forwardService, ok := forwardServiceRaw.(types.ForwardRuleManager)
	if !ok {
		return fmt.Errorf("ForwardRuleService未实现ForwardRuleManager接口")
	}

	// 创建事件管理器
	b.eventManager = handlers.NewEventManager(b.session, b.config, filterService)

	// 注册默认事件处理器并注入转发服务
	if err := b.registerDefaultHandlersWithServices(b.eventManager, forwardService); err != nil {
		return fmt.Errorf("注册默认事件处理器失败: %w", err)
	}

	// 设置事件监听器
	b.eventManager.SetupEventListeners(b.client)

	logger.Info("✅ 事件处理器注册完成")
	return nil
}

// registerDefaultHandlersWithServices 注册默认事件处理器并注入服务依赖
func (b *Bot) registerDefaultHandlersWithServices(eventManager *handlers.EventManager, forwardService types.ForwardRuleManager) error {
	// 创建消息创建处理器并注入转发服务
	messageCreateHandler := events.NewMessageCreateHandler(b.config)
	messageCreateHandler.SetForwardService(forwardService)

	// 注册所有默认处理器
	defaultHandlers := []types.EventHandler{
		messageCreateHandler, // 使用注入了转发服务的处理器
		events.NewMessageUpdateHandler(),
		events.NewMessageDeleteHandler(),
	}

	// 获取过滤服务用于反应处理器
	filterServiceRaw, err := b.serviceManager.GetService("FilterRuleService")
	if err == nil {
		if filterService, ok := filterServiceRaw.(types.FilterEngine); ok {
			defaultHandlers = append(defaultHandlers,
				events.NewMessageReactionAddHandler(b.config, filterService),
				events.NewMessageReactionRemoveHandler(b.config, filterService),
			)
		}
	}

	// 注册所有处理器
	for _, handler := range defaultHandlers {
		if err := eventManager.RegisterHandler(handler); err != nil {
			return fmt.Errorf("注册事件处理器失败: %w", err)
		}
	}

	logger.Info("默认事件处理器注册完成", "count", len(defaultHandlers))
	return nil
}

// GetServiceManager 获取服务管理器
func (b *Bot) GetServiceManager() *services.ServiceManager {
	return b.serviceManager
}

// GetHandlerRegistry 获取处理器注册表
func (b *Bot) GetHandlerRegistry() *handlers.HandlerRegistry {
	return b.handlerRegistry
}

// GetCacheService 获取缓存服务
func (b *Bot) GetCacheService() types.CacheService {
	if service, err := b.serviceManager.GetService("cache"); err == nil {
		if cacheAdapter, ok := service.(*services.CacheServiceAdapter); ok {
			return cacheAdapter.GetService()
		}
	}
	return nil
}

// GetQueueService 获取队列服务
func (b *Bot) GetQueueService() types.QueueService {
	if service, err := b.serviceManager.GetService("queue"); err == nil {
		if queueAdapter, ok := service.(*services.QueueServiceAdapter); ok {
			return queueAdapter.GetService()
		}
	}
	return nil
}

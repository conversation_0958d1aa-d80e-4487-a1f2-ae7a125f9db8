package utils

import (
	"encoding/json"
	"fmt"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
	"github.com/tidwall/gjson"
)

// ProductExtractor 统一的产品信息提取器
// 整合了 types.ProductExtractor 的核心功能和转发服务特定的需求
// 支持简单提取和高级映射两种模式
type ProductExtractor struct {
	// 核心产品提取器
	coreExtractor *types.ProductExtractor
}

// NewProductExtractor 创建新的转发产品信息提取器
func NewProductExtractor() *ProductExtractor {
	return &ProductExtractor{
		coreExtractor: types.NewProductExtractor(),
	}
}

// ExtractFromMessage 从Discord消息中提取产品信息列表
// 这是主要的提取方法，整合了多种提取策略
func (fpe *ProductExtractor) ExtractFromMessage(message *discordgo.Message) ([]*types.ProductItem, error) {
	var allProductItems []*types.ProductItem

	// 如果没有embeds，尝试从消息内容提取基础信息
	if len(message.Embeds) == 0 {
		if message.Content != "" {
			product := &types.ProductItem{
				Title:        message.Content,
				Metadata:     make(map[string]interface{}),
				Stock:        0,
				Availability: "unknown",
			}
			allProductItems = append(allProductItems, product)
		}
		return allProductItems, nil
	}

	// 处理每个embed
	for _, embed := range message.Embeds {
		embedData := fpe.ConvertEmbedToMap(embed)
		extractedData := fpe.ExtractFromEmbedData(embedData)
		productItem := fpe.CreateProductFromExtractedData(extractedData)
		if productItem != nil {
			allProductItems = append(allProductItems, productItem)
		}
	}

	return allProductItems, nil
}

// ExtractProductIDFromMessage 从Discord消息中提取单个ProductID
// 这是为了兼容需要单个ProductID字符串的场景
func (fpe *ProductExtractor) ExtractProductIDFromMessage(message *discordgo.Message) (string, error) {
	products, err := fpe.ExtractFromMessage(message)
	if err != nil {
		return "", err
	}

	// 如果没有提取到任何产品，返回空字符串
	if len(products) == 0 {
		return "", nil
	}

	// 返回第一个产品的ProductID
	firstProduct := products[0]
	if firstProduct.ProductID != "" {
		return firstProduct.ProductID, nil
	}

	// 如果ProductID为空，尝试使用Title作为fallback
	if firstProduct.Title != "" {
		logger.Debug("ProductID为空，使用Title作为fallback", "title", firstProduct.Title)
		return firstProduct.Title, nil
	}

	return "", nil
}

// ExtractWithMappingResult 高级产品信息提取，支持映射服务集成
// 返回提取的产品信息、映射后的embeds和详细的处理结果
type ExtractWithMappingResult struct {
	Content      string                    `json:"content"`       // 映射后的内容
	Embeds       []*discordgo.MessageEmbed `json:"embeds"`        // 映射后的embeds
	Products     []*types.ProductItem      `json:"products"`      // 提取的产品信息
	MappingGroup string                    `json:"mapping_group"` // 使用的映射组
	Errors       []string                  `json:"errors"`        // 处理过程中的错误
	Warnings     []string                  `json:"warnings"`      // 处理过程中的警告
}

// MappingServiceInterface 映射服务接口，避免循环依赖
type MappingServiceInterface interface {
	FindMappingGroupByAuthor(authorName string) (string, error)
	MapFields(extractedData map[string]interface{}, mappingGroupName string) (*types.MappingResult, error)
}

// ExtractWithMapping 使用映射服务进行高级产品信息提取
func (fpe *ProductExtractor) ExtractWithMapping(message *discordgo.Message, mappingService MappingServiceInterface, defaultMappingGroup string) *ExtractWithMappingResult {
	result := &ExtractWithMappingResult{
		Content:      message.Content,
		Embeds:       message.Embeds,
		Products:     make([]*types.ProductItem, 0),
		MappingGroup: defaultMappingGroup,
		Errors:       make([]string, 0),
		Warnings:     make([]string, 0),
	}

	// 如果没有映射服务，使用基础提取
	if mappingService == nil {
		products, err := fpe.ExtractFromMessage(message)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("基础产品信息提取失败: %v", err))
			return result
		}
		result.Products = products
		return result
	}

	// 将Discord消息转换为map格式用于映射
	messageData := fpe.ConvertDiscordMessageToMap(message)

	// 检查是否包含 embeds 字段
	embeds, hasEmbeds := messageData["embeds"].([]map[string]interface{})
	if !hasEmbeds || len(embeds) == 0 {
		result.Warnings = append(result.Warnings, "消息不包含embeds或embeds为空")
		return result
	}

	mappedEmbeds := make([]*discordgo.MessageEmbed, 0, len(embeds))

	for i, embedData := range embeds {
		// 使用统一的数据提取流程
		extractedData := fpe.ExtractFromEmbedData(embedData)

		// 根据AuthorName自动选择映射组
		mappingGroupName := fpe.selectMappingGroup(extractedData, mappingService, defaultMappingGroup, result)

		// 应用字段映射
		mappingResult, err := mappingService.MapFields(extractedData, mappingGroupName)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("embed[%d]字段映射失败: %v", i, err))
			// 映射失败时，创建基础产品信息
			productItem := fpe.CreateProductFromExtractedData(extractedData)
			if productItem != nil {
				result.Products = append(result.Products, productItem)
			}
			mappedEmbeds = append(mappedEmbeds, fpe.convertMapToDiscordEmbed(embedData))
		} else {
			// 映射成功，使用映射结果
			if mappingResult.Product != nil {
				result.Products = append(result.Products, mappingResult.Product)

				// 记录详细的字段映射信息到日志
				for _, appliedRule := range mappingResult.AppliedRules {
					logger.Info("字段映射详情",
						"source_field", appliedRule.SourceField,
						"target_field", appliedRule.TargetField,
						"matched_field", appliedRule.MatchedField,
						"used_fallback", appliedRule.UsedFallback,
						"fallback_index", appliedRule.FallbackIndex,
						"value", appliedRule.Value,
						"transform", appliedRule.Transform)
				}

				logger.Debug("字段映射成功",
					"mapping_group", mappingGroupName,
					"applied_rules", len(mappingResult.AppliedRules),
					"errors", len(mappingResult.Errors),
					"warnings", len(mappingResult.Warnings))
			}

			// 合并映射结果的错误和警告
			for _, err := range mappingResult.Errors {
				result.Errors = append(result.Errors, err.Message)
			}
			for _, warning := range mappingResult.Warnings {
				result.Warnings = append(result.Warnings, warning.Message)
			}

			mappedEmbeds = append(mappedEmbeds, fpe.convertMapToDiscordEmbed(embedData))
		}
	}

	// 更新结果
	result.Embeds = mappedEmbeds
	result.MappingGroup = result.MappingGroup // 保持最后使用的映射组

	// 生成映射后的内容
	if len(result.Products) > 0 {
		var contentParts []string
		for _, product := range result.Products {
			if product.Title != "" {
				contentParts = append(contentParts, product.Title)
			}
		}
		if len(contentParts) > 0 {
			result.Content = fmt.Sprintf("%s\n%s", message.Content, fmt.Sprintf("提取的产品: %s", fmt.Sprintf("[%s]", fmt.Sprintf("%s", contentParts))))
		}
	}

	return result
}

// selectMappingGroup 智能选择映射组
func (fpe *ProductExtractor) selectMappingGroup(extractedData map[string]interface{}, mappingService MappingServiceInterface, defaultGroup string, result *ExtractWithMappingResult) string {
	// 根据AuthorName自动选择映射组
	if authorName, ok := extractedData["AuthorName"].(string); ok && authorName != "" {
		// 根据AuthorName查找映射组
		foundGroup, err := mappingService.FindMappingGroupByAuthor(authorName)
		if err != nil {
			result.Warnings = append(result.Warnings, fmt.Sprintf("根据AuthorName查找映射组失败: %v", err))
			return defaultGroup
		} else {
			result.MappingGroup = foundGroup
			logger.Info("根据AuthorName自动选择映射组",
				"author_name", authorName,
				"selected_group", foundGroup,
				"default_group", defaultGroup)
			return foundGroup
		}
	}

	// 如果没有AuthorName，使用默认映射组
	if defaultGroup == "" {
		return "default"
	}
	return defaultGroup
}

// convertMapToDiscordEmbed 将map转换为Discord embed
func (fpe *ProductExtractor) convertMapToDiscordEmbed(embedData map[string]interface{}) *discordgo.MessageEmbed {
	embed := &discordgo.MessageEmbed{}

	if title, ok := embedData["title"].(string); ok {
		embed.Title = title
	}
	if description, ok := embedData["description"].(string); ok {
		embed.Description = description
	}
	if url, ok := embedData["url"].(string); ok {
		embed.URL = url
	}
	if color, ok := embedData["color"].(int); ok {
		embed.Color = color
	}

	// 处理图片
	if imageData, ok := embedData["image"].(map[string]interface{}); ok {
		if imageURL, ok := imageData["url"].(string); ok {
			embed.Image = &discordgo.MessageEmbedImage{URL: imageURL}
		}
	}

	// 处理缩略图
	if thumbnailData, ok := embedData["thumbnail"].(map[string]interface{}); ok {
		if thumbnailURL, ok := thumbnailData["url"].(string); ok {
			embed.Thumbnail = &discordgo.MessageEmbedThumbnail{URL: thumbnailURL}
		}
	}

	// 处理作者信息
	if authorData, ok := embedData["author"].(map[string]interface{}); ok {
		author := &discordgo.MessageEmbedAuthor{}
		if name, ok := authorData["name"].(string); ok {
			author.Name = name
		}
		if url, ok := authorData["url"].(string); ok {
			author.URL = url
		}
		if iconURL, ok := authorData["icon_url"].(string); ok {
			author.IconURL = iconURL
		}
		embed.Author = author
	}

	// 处理页脚信息
	if footerData, ok := embedData["footer"].(map[string]interface{}); ok {
		footer := &discordgo.MessageEmbedFooter{}
		if text, ok := footerData["text"].(string); ok {
			footer.Text = text
		}
		if iconURL, ok := footerData["icon_url"].(string); ok {
			footer.IconURL = iconURL
		}
		embed.Footer = footer
	}

	// 处理字段
	if fieldsData, ok := embedData["fields"].([]interface{}); ok {
		fields := make([]*discordgo.MessageEmbedField, 0, len(fieldsData))
		for _, fieldData := range fieldsData {
			if fieldMap, ok := fieldData.(map[string]interface{}); ok {
				field := &discordgo.MessageEmbedField{}
				if name, ok := fieldMap["name"].(string); ok {
					field.Name = name
				}
				if value, ok := fieldMap["value"].(string); ok {
					field.Value = value
				}
				if inline, ok := fieldMap["inline"].(bool); ok {
					field.Inline = inline
				}
				fields = append(fields, field)
			}
		}
		embed.Fields = fields
	}

	// 处理时间戳
	if timestamp, ok := embedData["timestamp"].(string); ok {
		embed.Timestamp = timestamp
	}

	return embed
}

// ExtractFromEmbedData 统一的embed数据提取方法（使用gjson）
// 将embed数据提取为标准化的map，键名与ProductItem字段名一致
// 这个方法整合了原 forward/service.go 中的 extractEmbedDataForMapping 逻辑
func (fpe *ProductExtractor) ExtractFromEmbedData(embedData map[string]interface{}) map[string]interface{} {
	extractedData := make(map[string]interface{})

	// 将embedData转换为JSON字符串，以便使用gjson
	jsonData, err := json.Marshal(embedData)
	if err != nil {
		logger.Error("转换embed数据为JSON失败", "error", err)
		return extractedData
	}
	jsonStr := string(jsonData)

	// 使用gjson提取基础字段，键名与ProductItem字段名一致
	if title := gjson.Get(jsonStr, "title").String(); title != "" {
		extractedData["Title"] = title
	}

	if description := gjson.Get(jsonStr, "description").String(); description != "" {
		extractedData["Description"] = description
	}

	if url := gjson.Get(jsonStr, "url").String(); url != "" {
		extractedData["URL"] = url
	}

	if color := gjson.Get(jsonStr, "color"); color.Exists() {
		extractedData["Color"] = color.Int()
	}

	// 提取图片信息
	if imageURL := gjson.Get(jsonStr, "image.url").String(); imageURL != "" {
		extractedData["ImageURL"] = imageURL
	}

	if thumbnailURL := gjson.Get(jsonStr, "thumbnail.url").String(); thumbnailURL != "" {
		extractedData["ThumbnailURL"] = thumbnailURL
	}

	// 提取作者信息
	if authorName := gjson.Get(jsonStr, "author.name").String(); authorName != "" {
		extractedData["AuthorName"] = authorName
	}

	if authorURL := gjson.Get(jsonStr, "author.url").String(); authorURL != "" {
		extractedData["AuthorURL"] = authorURL
	}

	if authorIconURL := gjson.Get(jsonStr, "author.icon_url").String(); authorIconURL != "" {
		extractedData["AuthorIconURL"] = authorIconURL
	}

	// 提取页脚信息
	if footerText := gjson.Get(jsonStr, "footer.text").String(); footerText != "" {
		extractedData["FooterText"] = footerText
	}

	if footerIconURL := gjson.Get(jsonStr, "footer.icon_url").String(); footerIconURL != "" {
		extractedData["FooterIconURL"] = footerIconURL
	}

	// 提取时间戳
	if timestamp := gjson.Get(jsonStr, "timestamp").String(); timestamp != "" {
		extractedData["Timestamp"] = timestamp
	}

	// 使用gjson处理fields字段，将其展开为键值对（保持原始字段名，合并重复字段）
	fieldsResult := gjson.Get(jsonStr, "fields")
	if fieldsResult.Exists() && fieldsResult.IsArray() {
		logger.Info("开始处理fields字段", "fields_count", len(fieldsResult.Array()))

		// 用于跟踪重复字段
		fieldCounts := make(map[string]int)

		fieldsResult.ForEach(func(key, value gjson.Result) bool {
			name := value.Get("name").String()
			fieldValue := value.Get("value").String()

			if name != "" && fieldValue != "" {
				// 检查是否已存在相同字段名
				if existingValue, exists := extractedData[name]; exists {
					// 字段已存在，合并值
					fieldCounts[name]++

					// 将现有值转换为字符串
					existingStr := fmt.Sprintf("%v", existingValue)

					// 合并值，使用分隔符
					mergedValue := existingStr + " | " + fieldValue
					extractedData[name] = mergedValue

					logger.Info("字段合并成功",
						"field_name", name,
						"existing_value", existingStr,
						"new_value", fieldValue,
						"merged_value", mergedValue,
						"occurrence_count", fieldCounts[name]+1)
				} else {
					// 首次出现的字段
					extractedData[name] = fieldValue
					fieldCounts[name] = 0

					logger.Info("字段提取成功",
						"field_name", name,
						"value", fieldValue)
				}
			}

			return true // 继续遍历
		})

		// 记录重复字段统计
		duplicateCount := 0
		for fieldName, count := range fieldCounts {
			if count > 0 {
				duplicateCount++
				logger.Info("发现重复字段",
					"field_name", fieldName,
					"total_occurrences", count+1)
			}
		}

		if duplicateCount > 0 {
			logger.Info("字段合并统计",
				"total_fields", len(fieldCounts),
				"duplicate_fields", duplicateCount)
		}
	}

	logger.Info("embed数据提取完成", "extracted_fields", len(extractedData))
	return extractedData
}

// CreateProductFromExtractedData 从提取的数据创建ProductItem（fallback方法）
// 这个方法整合了原 forward/service.go 中的 createProductFromExtractedData 逻辑
func (fpe *ProductExtractor) CreateProductFromExtractedData(extractedData map[string]interface{}) *types.ProductItem {
	product := &types.ProductItem{
		Metadata:     make(map[string]interface{}),
		Stock:        0,
		Availability: "unknown",
	}

	// 从提取的数据中设置基础字段
	if title, ok := extractedData["Title"].(string); ok && title != "" {
		product.Title = title
	}

	if url, ok := extractedData["URL"].(string); ok && url != "" {
		product.URL = url
	}

	if productID, ok := extractedData["ProductID"].(string); ok && productID != "" {
		product.ProductID = productID
	}

	if price, ok := extractedData["Price"].(string); ok && price != "" {
		product.Price = price
	}

	// 设置其他字段
	if description, ok := extractedData["Description"].(string); ok && description != "" {
		product.Description = description
	}

	if color, ok := extractedData["Color"].(int64); ok {
		product.Color = int(color)
	}

	if imageURL, ok := extractedData["ImageURL"].(string); ok && imageURL != "" {
		product.ImageURL = imageURL
	}

	if thumbnailURL, ok := extractedData["ThumbnailURL"].(string); ok && thumbnailURL != "" {
		product.ThumbnailURL = thumbnailURL
	}

	if authorName, ok := extractedData["AuthorName"].(string); ok && authorName != "" {
		product.AuthorName = authorName
	}

	if authorURL, ok := extractedData["AuthorURL"].(string); ok && authorURL != "" {
		product.AuthorURL = authorURL
	}

	if authorIconURL, ok := extractedData["AuthorIconURL"].(string); ok && authorIconURL != "" {
		product.AuthorIconURL = authorIconURL
	}

	if footerText, ok := extractedData["FooterText"].(string); ok && footerText != "" {
		product.FooterText = footerText
	}

	if footerIconURL, ok := extractedData["FooterIconURL"].(string); ok && footerIconURL != "" {
		product.FooterIconURL = footerIconURL
	}

	if timestamp, ok := extractedData["Timestamp"].(string); ok && timestamp != "" {
		product.Timestamp = timestamp
	}

	// 将其他字段存储到元数据中
	for key, value := range extractedData {
		// 跳过已经映射的标准字段
		if !fpe.isStandardField(key) {
			product.Metadata[key] = value
		}
	}

	// 如果没有基本信息，返回nil
	if product.Title == "" && product.URL == "" && product.ProductID == "" {
		return nil
	}

	return product
}

// ConvertDiscordMessageToMap 将Discord消息转换为map格式
// 这个方法整合了原 forward/service.go 中的 convertDiscordMessageToMap 逻辑
func (fpe *ProductExtractor) ConvertDiscordMessageToMap(message *discordgo.Message) map[string]interface{} {
	messageMap := map[string]interface{}{
		"content":    message.Content,
		"author_id":  message.Author.ID,
		"author":     message.Author.Username,
		"channel_id": message.ChannelID,
		"guild_id":   message.GuildID,
		"message_id": message.ID,
		"timestamp":  message.Timestamp,
	}

	// 转换embeds
	if len(message.Embeds) > 0 {
		embeds := make([]map[string]interface{}, len(message.Embeds))
		for i, embed := range message.Embeds {
			embeds[i] = fpe.ConvertEmbedToMap(embed)
		}
		messageMap["embeds"] = embeds
	}

	return messageMap
}

// ConvertEmbedToMap 将单个Discord embed转换为map格式
// 这个方法整合了原 forward/service.go 中的 convertEmbedToMap 逻辑
func (fpe *ProductExtractor) ConvertEmbedToMap(embed *discordgo.MessageEmbed) map[string]interface{} {
	embedMap := map[string]interface{}{
		"title":       embed.Title,
		"description": embed.Description,
		"url":         embed.URL,
		"color":       embed.Color,
	}

	// 添加图片信息
	if embed.Image != nil {
		embedMap["image"] = map[string]interface{}{
			"url": embed.Image.URL,
		}
	}

	// 添加缩略图信息
	if embed.Thumbnail != nil {
		embedMap["thumbnail"] = map[string]interface{}{
			"url": embed.Thumbnail.URL,
		}
	}

	// 添加作者信息
	if embed.Author != nil {
		authorMap := make(map[string]interface{})
		if embed.Author.Name != "" {
			authorMap["name"] = embed.Author.Name
		}
		if embed.Author.URL != "" {
			authorMap["url"] = embed.Author.URL
		}
		if embed.Author.IconURL != "" {
			authorMap["icon_url"] = embed.Author.IconURL
		}
		if len(authorMap) > 0 {
			embedMap["author"] = authorMap
		}
	}

	// 添加页脚信息
	if embed.Footer != nil {
		footerMap := make(map[string]interface{})
		if embed.Footer.Text != "" {
			footerMap["text"] = embed.Footer.Text
		}
		if embed.Footer.IconURL != "" {
			footerMap["icon_url"] = embed.Footer.IconURL
		}
		if len(footerMap) > 0 {
			embedMap["footer"] = footerMap
		}
	}

	// 添加时间戳
	if embed.Timestamp != "" {
		embedMap["timestamp"] = embed.Timestamp
	}

	// 添加字段信息
	if embed.Fields != nil {
		fields := make([]map[string]interface{}, len(embed.Fields))
		for j, field := range embed.Fields {
			fields[j] = map[string]interface{}{
				"name":   field.Name,
				"value":  field.Value,
				"inline": field.Inline,
			}
		}
		embedMap["fields"] = fields
	}

	return embedMap
}

// isStandardField 检查字段是否为标准ProductItem字段
func (fpe *ProductExtractor) isStandardField(fieldName string) bool {
	standardFields := []string{
		"Title", "URL", "Description", "Color", "ProductID", "Price",
		"ImageURL", "ThumbnailURL", "FooterText", "FooterIconURL",
		"AuthorName", "AuthorURL", "AuthorIconURL", "Timestamp",
	}

	for _, field := range standardFields {
		if fieldName == field {
			return true
		}
	}
	return false
}

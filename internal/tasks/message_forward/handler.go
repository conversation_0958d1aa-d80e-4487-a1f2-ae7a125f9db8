package message_forward

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/tasks"
	"zeka-go/internal/types"
)

// MessageForwardTaskHandler 消息转发任务处理器
type MessageForwardTaskHandler struct {
	*tasks.BaseTaskHandler

	// 服务依赖（运行时注入）
	forwardService      types.ForwardRuleManager
	notificationService types.ProductNotificationService
}

// NewMessageForwardTaskHandler 创建新的消息转发任务处理器
func NewMessageForwardTaskHandler() *MessageForwardTaskHandler {
	handler := &MessageForwardTaskHandler{
		BaseTaskHandler: tasks.NewBaseTaskHandler(
			"message_forward",
			"处理频道间的延迟消息转发任务",
		),
	}

	return handler
}

// SetForwardService 设置转发服务（运行时注入）
func (h *MessageForwardTaskHandler) SetForwardService(forwardService types.ForwardRuleManager) {
	h.forwardService = forwardService
}

// SetNotificationService 设置通知服务（运行时注入）
func (h *MessageForwardTaskHandler) SetNotificationService(notificationService types.ProductNotificationService) {
	h.notificationService = notificationService
	logger.Debug("通知服务已注入到消息转发任务处理器")
}

// SetTemplateServices 设置模板服务（向后兼容，已弃用）
func (h *MessageForwardTaskHandler) SetTemplateServices(manager interface{}, renderer interface{}) {
	logger.Warn("SetTemplateServices已弃用，消息转发现在使用通知服务")
}

// getForwardService 获取转发服务（优先使用注入的服务，否则从客户端获取）
func (h *MessageForwardTaskHandler) getForwardService(client *types.Client) types.ForwardRuleManager {
	// 优先使用注入的服务
	if h.forwardService != nil {
		logger.Debug("使用注入的转发服务")
		return h.forwardService
	}

	// 从客户端服务容器获取
	if client != nil && client.Services != nil && client.Services.ForwardService != nil {
		logger.Debug("从客户端服务容器获取转发服务")
		return client.Services.ForwardService
	}

	logger.Debug("无法获取转发服务")
	return nil
}

// getNotificationService 获取通知服务（优先使用注入的服务，否则从客户端获取）
func (h *MessageForwardTaskHandler) getNotificationService(client *types.Client) types.ProductNotificationService {
	// 优先使用注入的服务
	if h.notificationService != nil {
		logger.Debug("使用注入的通知服务")
		return h.notificationService
	}

	// 从客户端服务容器获取
	if client != nil && client.Services != nil && client.Services.ProductNotificationService != nil {
		logger.Debug("从客户端服务容器获取产品通知服务")
		return client.Services.ProductNotificationService
	}

	logger.Debug("无法获取通知服务")
	return nil
}

// parseTaskData 解析任务数据，支持多种输入格式
func (h *MessageForwardTaskHandler) parseTaskData(data any) (*MessageForwardTask, error) {
	if data == nil {
		return nil, fmt.Errorf("任务数据为空")
	}

	// 记录原始数据用于调试
	logger.Debug("解析任务数据", "data_type", fmt.Sprintf("%T", data), "data", data)

	var jsonData []byte
	var err error

	// 处理不同的输入数据格式
	switch v := data.(type) {
	case []byte:
		// 直接使用字节数组
		jsonData = v
	case string:
		// 字符串格式的JSON
		jsonData = []byte(v)
	case map[string]interface{}:
		// 检查是否是队列任务格式（包含嵌套的 data 字段）
		if taskData, ok := v["data"]; ok {
			logger.Debug("检测到队列任务格式，提取 data 字段")
			// 重新序列化 data 字段
			jsonData, err = json.Marshal(taskData)
			if err != nil {
				return nil, fmt.Errorf("序列化队列任务数据失败: %w", err)
			}
		} else {
			// 直接序列化整个 map
			jsonData, err = json.Marshal(v)
			if err != nil {
				return nil, fmt.Errorf("序列化任务数据失败: %w", err)
			}
		}
	default:
		// 对于其他类型，尝试 JSON 序列化
		jsonData, err = json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("序列化未知类型任务数据失败: %w", err)
		}
	}

	// 反序列化为 MessageForwardTask
	var task MessageForwardTask
	if err := json.Unmarshal(jsonData, &task); err != nil {
		logger.Error("反序列化任务数据失败", "error", err, "json_data", string(jsonData))
		return nil, fmt.Errorf("反序列化任务数据失败: %w", err)
	}

	return &task, nil
}

// Handle 处理消息转发任务
func (h *MessageForwardTaskHandler) Handle(ctx context.Context, client *types.Client, data any) error {
	startTime := time.Now()

	// 检查转发服务可用性
	forwardServiceAvailable := h.getForwardService(client) != nil

	logger.Info("🚀 开始处理消息转发任务",
		"handler", h.GetType(),
		"forward_service_injected", h.forwardService != nil,
		"forward_service_available", forwardServiceAvailable)

	// 解析任务数据
	task, err := h.parseTaskData(data)
	if err != nil {
		return fmt.Errorf("解析任务数据失败: %w", err)
	}

	// 验证任务数据
	if err := task.Validate(); err != nil {
		logger.Error("任务数据验证失败",
			"task_id", task.ID,
			"original_message", task.OriginalMessage,
			"original_message_length", len(task.OriginalMessage),
			"source_channel", task.SourceChannel,
			"target_channels", task.TargetChannels,
			"author_id", task.AuthorID,
			"guild_id", task.GuildID,
			"error", err)
		return fmt.Errorf("任务数据验证失败: %w", err)
	}

	logger.Info("处理消息转发任务",
		"task_id", task.ID,
		"mapping", task.MappingName,
		"source_channel", task.SourceChannel,
		"target_channels", len(task.TargetChannels),
		"original_message_length", len(task.OriginalMessage))

	// 直接使用原始消息内容，如果为空则使用占位符
	processedMessage := task.OriginalMessage
	if strings.TrimSpace(processedMessage) == "" {
		processedMessage = "[无文本内容]" // 默认占位符
	}

	task.ProcessedMessage = processedMessage

	logger.Debug("消息处理完成",
		"task_id", task.ID,
		"original_length", len(task.OriginalMessage),
		"processed_length", len(task.ProcessedMessage),
		"processed_message", task.ProcessedMessage)

	// 发送消息到目标频道
	if err := h.sendToTargetChannels(ctx, client, task); err != nil {
		return fmt.Errorf("发送消息失败: %w", err)
	}

	// 记录处理完成
	duration := time.Since(startTime)
	logger.Info("消息转发任务处理完成",
		"task_id", task.ID,
		"duration", duration,
		"target_channels", len(task.TargetChannels))

	return nil
}

// Validate 验证任务数据
func (h *MessageForwardTaskHandler) Validate(data any) error {
	if data == nil {
		return types.ErrInvalidTaskData
	}

	// 尝试解析任务数据
	_, err := h.parseTaskData(data)
	return err
}

// sendToTargetChannels 发送消息到目标频道（使用通知服务）
func (h *MessageForwardTaskHandler) sendToTargetChannels(ctx context.Context, client *types.Client, task *MessageForwardTask) error {
	// 获取通知服务
	notificationService := h.getNotificationService(client)
	if notificationService == nil {
		logger.Debug("通知服务不可用，使用回退方法")
		return h.sendToTargetChannelsLegacy(ctx, client, task)
	}

	successCount := 0
	var lastError error

	for _, channelID := range task.TargetChannels {
		logger.Debug("使用通知服务发送消息到频道", "channel", channelID, "task_id", task.ID)

		// 优先处理ProductItem（新格式）
		if len(task.Products) > 0 {
			// 使用通知服务发送产品通知
			if err := h.sendProductNotifications(ctx, notificationService, task.Products, channelID); err != nil {
				logger.Error("发送产品通知失败", "error", err, "channel", channelID, "task_id", task.ID)
				lastError = err
				continue
			}
		} else {
			// 处理非产品消息（向后兼容）
			if err := h.sendLegacyMessage(ctx, task, channelID); err != nil {
				logger.Error("发送传统消息失败", "error", err, "channel", channelID, "task_id", task.ID)
				lastError = err
				continue
			}
		}

		successCount++
		logger.Debug("消息发送成功", "channel", channelID, "task_id", task.ID)
	}

	// 检查发送结果
	if successCount == 0 {
		return fmt.Errorf("所有频道消息发送失败，最后错误: %w", lastError)
	}

	if successCount < len(task.TargetChannels) {
		logger.Warn("部分频道消息发送失败",
			"success", successCount,
			"total", len(task.TargetChannels),
			"task_id", task.ID)
	}

	logger.Info("消息转发完成",
		"task_id", task.ID,
		"success_count", successCount,
		"total_channels", len(task.TargetChannels))

	return nil
}

// sendProductNotifications 使用通知服务发送产品通知
func (h *MessageForwardTaskHandler) sendProductNotifications(ctx context.Context, notificationService types.ProductNotificationService, products []*types.ProductItem, channelID string) error {
	for _, product := range products {
		if product == nil {
			continue
		}

		// 使用通知服务发送产品通知
		result, err := notificationService.SendProductNotification(ctx, product, channelID)
		if err != nil {
			return fmt.Errorf("发送产品通知失败: %w", err)
		}

		logger.Debug("产品通知发送成功",
			"notification_id", result.ID,
			"product_platform", product.Platform,
			"channel", channelID)
	}

	return nil
}

// sendLegacyMessage 发送传统消息（向后兼容）
func (h *MessageForwardTaskHandler) sendLegacyMessage(ctx context.Context, task *MessageForwardTask, channelID string) error {
	// 对于非产品消息，暂时回退到传统方式
	// TODO: 未来可以考虑创建通用的消息通知类型
	logger.Debug("发送传统消息（向后兼容）", "channel", channelID, "task_id", task.ID)

	// 这里可以实现简单的文本消息发送，或者回退到旧逻辑
	// 为了简化，我们暂时跳过非产品消息的处理
	logger.Warn("跳过非产品消息的发送", "task_id", task.ID, "channel", channelID)
	return nil
}

// sendToTargetChannelsLegacy 回退方法：当通知服务不可用时使用
func (h *MessageForwardTaskHandler) sendToTargetChannelsLegacy(ctx context.Context, client *types.Client, task *MessageForwardTask) error {
	if client.Session == nil {
		return fmt.Errorf("discord session 未初始化")
	}

	// 检查处理后的消息是否为空
	if strings.TrimSpace(task.ProcessedMessage) == "" {
		logger.Error("处理后的消息为空，无法发送", "task_id", task.ID)
		return fmt.Errorf("处理后的消息为空，Discord不允许发送空消息")
	}

	successCount := 0
	var lastError error

	for _, channelID := range task.TargetChannels {
		logger.Debug("使用回退方法发送消息到频道", "channel", channelID, "task_id", task.ID)

		// 简化的消息发送：只发送文本内容
		_, err := client.Session.ChannelMessageSend(channelID, task.ProcessedMessage)
		if err != nil {
			logger.Error("发送消息到频道失败", "error", err, "channel", channelID, "task_id", task.ID)
			lastError = err
			continue
		}

		successCount++
		logger.Debug("消息发送成功", "channel", channelID, "task_id", task.ID)
	}

	// 检查发送结果
	if successCount == 0 {
		return fmt.Errorf("所有频道消息发送失败，最后错误: %w", lastError)
	}

	logger.Info("消息转发完成（回退模式）",
		"task_id", task.ID,
		"success_count", successCount,
		"total_channels", len(task.TargetChannels))

	return nil
}

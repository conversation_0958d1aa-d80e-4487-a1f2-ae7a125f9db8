package events

import (
	"context"
	"testing"

	"zeka-go/internal/types"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockForwardRuleManager 模拟转发规则管理器
type MockForwardRuleManager struct {
	mock.Mock
}

func (m *MockForwardRuleManager) AddRule(rule *types.ForwardRule) error {
	args := m.Called(rule)
	return args.Error(0)
}

func (m *MockForwardRuleManager) RemoveRule(ruleName string) error {
	args := m.Called(ruleName)
	return args.Error(0)
}

func (m *MockForwardRuleManager) UpdateRule(rule *types.ForwardRule) error {
	args := m.Called(rule)
	return args.Error(0)
}

func (m *MockForwardRuleManager) GetRule(ruleName string) (*types.ForwardRule, error) {
	args := m.Called(ruleName)
	return args.Get(0).(*types.ForwardRule), args.Error(1)
}

func (m *MockForwardRuleManager) ListRules() []*types.ForwardRule {
	args := m.Called()
	return args.Get(0).([]*types.ForwardRule)
}

func (m *MockForwardRuleManager) GetRulesBySourceChannel(channelID string) []*types.ForwardRule {
	args := m.Called(channelID)
	return args.Get(0).([]*types.ForwardRule)
}

func (m *MockForwardRuleManager) GetRulesByTargetChannel(channelID string) []*types.ForwardRule {
	args := m.Called(channelID)
	return args.Get(0).([]*types.ForwardRule)
}

func (m *MockForwardRuleManager) ShouldForward(rule *types.ForwardRule, message interface{}) (bool, error) {
	args := m.Called(rule, message)
	return args.Bool(0), args.Error(1)
}

func (m *MockForwardRuleManager) ForwardMessage(rule *types.ForwardRule, message interface{}) error {
	args := m.Called(rule, message)
	return args.Error(0)
}

func (m *MockForwardRuleManager) GetRuleStats(ruleName string) (*types.ForwardRuleStats, error) {
	args := m.Called(ruleName)
	return args.Get(0).(*types.ForwardRuleStats), args.Error(1)
}

func (m *MockForwardRuleManager) UpdateRuleStats(ruleName string, stats *types.ForwardRuleStats) error {
	args := m.Called(ruleName, stats)
	return args.Error(0)
}

func TestNewMessageReactionAddHandler(t *testing.T) {
	config := &types.Config{}
	filterService := &MockFilterEngine{}

	handler := NewMessageReactionAddHandler(config, filterService)

	assert.NotNil(t, handler)
	assert.Equal(t, "MESSAGE_REACTION_ADD", handler.eventType)
	assert.Equal(t, 0, handler.priority)
	assert.Equal(t, config, handler.config)
	assert.Equal(t, filterService, handler.filterService)
	assert.NotNil(t, handler.productExtractor)
}

func TestNewMessageReactionRemoveHandler(t *testing.T) {
	config := &types.Config{}
	filterService := &MockFilterEngine{}

	handler := NewMessageReactionRemoveHandler(config, filterService)

	assert.NotNil(t, handler)
	assert.Equal(t, "MESSAGE_REACTION_REMOVE", handler.eventType)
	assert.Equal(t, 0, handler.priority)
	assert.Equal(t, config, handler.config)
	assert.Equal(t, filterService, handler.filterService)
	assert.NotNil(t, handler.productExtractor)
}

func TestMessageReactionAddHandler_hasForwardRules(t *testing.T) {
	config := &types.Config{}
	filterService := &MockFilterEngine{}
	handler := NewMessageReactionAddHandler(config, filterService)

	mockForwardService := &MockForwardRuleManager{}

	tests := []struct {
		name          string
		channelID     string
		sourceRules   []*types.ForwardRule
		targetRules   []*types.ForwardRule
		servicesNil   bool
		forwardSvcNil bool
		expected      bool
	}{
		{
			name:      "Has source rules",
			channelID: "channel1",
			sourceRules: []*types.ForwardRule{
				{Name: "rule1", InputChannel: "channel1"},
			},
			targetRules: []*types.ForwardRule{},
			expected:    true,
		},
		{
			name:        "Has target rules",
			channelID:   "channel1",
			sourceRules: []*types.ForwardRule{},
			targetRules: []*types.ForwardRule{
				{Name: "rule2", OutputChannel: "channel1"},
			},
			expected: true,
		},
		{
			name:        "No rules",
			channelID:   "channel1",
			sourceRules: []*types.ForwardRule{},
			targetRules: []*types.ForwardRule{},
			expected:    false,
		},
		{
			name:        "Services nil",
			channelID:   "channel1",
			servicesNil: true,
			expected:    false,
		},
		{
			name:          "ForwardService nil",
			channelID:     "channel1",
			forwardSvcNil: true,
			expected:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			client := &types.Client{}

			if !tt.servicesNil {
				client.Services = &types.ServiceContainer{}
				if !tt.forwardSvcNil {
					client.Services.ForwardService = mockForwardService
					mockForwardService.On("GetRulesBySourceChannel", tt.channelID).Return(tt.sourceRules)
					// 只有在没有源规则时才会检查目标规则
					if len(tt.sourceRules) == 0 {
						mockForwardService.On("GetRulesByTargetChannel", tt.channelID).Return(tt.targetRules)
					}
				}
			}

			result := handler.hasForwardRules(ctx, client, tt.channelID)
			assert.Equal(t, tt.expected, result)

			if !tt.servicesNil && !tt.forwardSvcNil {
				mockForwardService.AssertExpectations(t)
			}

			// 重置mock
			mockForwardService.ExpectedCalls = nil
			mockForwardService.Calls = nil
		})
	}
}

// 注意：由于discordgo版本兼容性问题，暂时跳过权限检查测试
// 这些测试需要在实际环境中验证discordgo.MessageReactionAdd和MessageReactionRemove的字段结构

// MockFilterEngine 模拟过滤引擎
type MockFilterEngine struct {
	mock.Mock
}

func (m *MockFilterEngine) AddRule(rule *types.FilterRule) error {
	args := m.Called(rule)
	return args.Error(0)
}

func (m *MockFilterEngine) RemoveRule(ruleID string) error {
	args := m.Called(ruleID)
	return args.Error(0)
}

func (m *MockFilterEngine) UpdateRule(rule *types.FilterRule) error {
	args := m.Called(rule)
	return args.Error(0)
}

func (m *MockFilterEngine) GetRule(ruleID string) (*types.FilterRule, error) {
	args := m.Called(ruleID)
	return args.Get(0).(*types.FilterRule), args.Error(1)
}

func (m *MockFilterEngine) ListRules(channelID string) []*types.FilterRule {
	args := m.Called(channelID)
	return args.Get(0).([]*types.FilterRule)
}

func (m *MockFilterEngine) CheckMessage(channelID string, message interface{}) (*types.FilterResult, error) {
	args := m.Called(channelID, message)
	return args.Get(0).(*types.FilterResult), args.Error(1)
}

func (m *MockFilterEngine) CheckProduct(channelID string, product *types.ProductItem) (*types.FilterResult, error) {
	args := m.Called(channelID, product)
	return args.Get(0).(*types.FilterResult), args.Error(1)
}

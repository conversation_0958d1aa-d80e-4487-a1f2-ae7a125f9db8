package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/google/uuid"
	amqp "github.com/rabbitmq/amqp091-go"
)

// ConsumerStatus 消费者状态
type ConsumerStatus int

const (
	ConsumerStatusStopped ConsumerStatus = iota
	ConsumerStatusStarting
	ConsumerStatusRunning
	ConsumerStatusError
	ConsumerStatusRestarting
)

// String 返回消费者状态的字符串表示
func (cs ConsumerStatus) String() string {
	switch cs {
	case ConsumerStatusStopped:
		return "stopped"
	case ConsumerStatusStarting:
		return "starting"
	case ConsumerStatusRunning:
		return "running"
	case ConsumerStatusError:
		return "error"
	case ConsumerStatusRestarting:
		return "restarting"
	default:
		return "unknown"
	}
}

// ConsumerInfo 消费者信息
type ConsumerInfo struct {
	QueueName    string
	Handler      types.MessageHandler
	Options      types.ConsumeOptions
	Context      context.Context
	Cancel       context.CancelFunc
	Status       ConsumerStatus
	StartTime    time.Time
	LastActivity time.Time
	ErrorCount   int64
	MessageCount int64
	LastError    error
	mu           sync.RWMutex
}

// UpdateStatus 更新消费者状态
func (ci *ConsumerInfo) UpdateStatus(status ConsumerStatus) {
	ci.mu.Lock()
	defer ci.mu.Unlock()
	ci.Status = status
	if status == ConsumerStatusRunning {
		ci.LastActivity = time.Now()
	}
}

// GetStatus 获取消费者状态
func (ci *ConsumerInfo) GetStatus() ConsumerStatus {
	ci.mu.RLock()
	defer ci.mu.RUnlock()
	return ci.Status
}

// IncrementMessageCount 增加消息计数
func (ci *ConsumerInfo) IncrementMessageCount() {
	ci.mu.Lock()
	defer ci.mu.Unlock()
	ci.MessageCount++
	ci.LastActivity = time.Now()
}

// IncrementErrorCount 增加错误计数
func (ci *ConsumerInfo) IncrementErrorCount(err error) {
	ci.mu.Lock()
	defer ci.mu.Unlock()
	ci.ErrorCount++
	ci.LastError = err
}

// GetStats 获取消费者统计信息
func (ci *ConsumerInfo) GetStats() (int64, int64, time.Time, error) {
	ci.mu.RLock()
	defer ci.mu.RUnlock()
	return ci.MessageCount, ci.ErrorCount, ci.LastActivity, ci.LastError
}

// ConsumerRegistry 消费者注册表
type ConsumerRegistry struct {
	consumers map[string]*ConsumerInfo
	mu        sync.RWMutex
}

// NewConsumerRegistry 创建新的消费者注册表
func NewConsumerRegistry() *ConsumerRegistry {
	return &ConsumerRegistry{
		consumers: make(map[string]*ConsumerInfo),
	}
}

// Register 注册消费者
func (cr *ConsumerRegistry) Register(queueName string, info *ConsumerInfo) {
	cr.mu.Lock()
	defer cr.mu.Unlock()
	cr.consumers[queueName] = info
	logger.Debug("消费者已注册", "queue", queueName, "status", info.Status.String())
}

// Unregister 注销消费者
func (cr *ConsumerRegistry) Unregister(queueName string) {
	cr.mu.Lock()
	defer cr.mu.Unlock()
	if info, exists := cr.consumers[queueName]; exists {
		if info.Cancel != nil {
			info.Cancel()
		}
		delete(cr.consumers, queueName)
		logger.Debug("消费者已注销", "queue", queueName)
	}
}

// Get 获取消费者信息
func (cr *ConsumerRegistry) Get(queueName string) (*ConsumerInfo, bool) {
	cr.mu.RLock()
	defer cr.mu.RUnlock()
	info, exists := cr.consumers[queueName]
	return info, exists
}

// List 列出所有消费者
func (cr *ConsumerRegistry) List() map[string]*ConsumerInfo {
	cr.mu.RLock()
	defer cr.mu.RUnlock()
	result := make(map[string]*ConsumerInfo)
	for k, v := range cr.consumers {
		result[k] = v
	}
	return result
}

// Count 获取消费者数量
func (cr *ConsumerRegistry) Count() int {
	cr.mu.RLock()
	defer cr.mu.RUnlock()
	return len(cr.consumers)
}

// UpdateStatus 更新消费者状态
func (cr *ConsumerRegistry) UpdateStatus(queueName string, status ConsumerStatus) {
	cr.mu.RLock()
	defer cr.mu.RUnlock()
	if info, exists := cr.consumers[queueName]; exists {
		info.UpdateStatus(status)
	}
}

// ConnectionPool 连接池
type ConnectionPool struct {
	connections []*amqp.Connection
	channels    []*amqp.Channel
	mu          sync.RWMutex
	size        int
	current     int
}

// HealthChecker 健康检查器
type HealthChecker struct {
	service  *RabbitMQService
	interval time.Duration
	stopCh   chan struct{}
}

// RabbitMQService RabbitMQ 队列服务实现
type RabbitMQService struct {
	config     types.QueueConfig
	connection *amqp.Connection
	channel    *amqp.Channel

	// 连接池
	pool *ConnectionPool

	// 健康检查
	healthChecker *HealthChecker

	// 任务处理器
	taskHandlers map[string]types.TaskHandler
	handlerMu    sync.RWMutex

	// 消费者管理
	consumerRegistry *ConsumerRegistry
	consumerWg       sync.WaitGroup
	restartChan      chan string // 用于通知消费者重启

	// 统计信息
	stats   *types.QueueStats
	statsMu sync.RWMutex

	// 状态管理
	isConnected bool
	connMu      sync.RWMutex

	// 重连管理
	reconnectAttempts    int
	maxReconnectAttempts int

	// 监控管理
	monitorStarted bool
	monitorMu      sync.Mutex

	// 上下文
	ctx    context.Context
	cancel context.CancelFunc
}

// NewRabbitMQService 创建新的 RabbitMQ 服务
func NewRabbitMQService(config types.QueueConfig) (*RabbitMQService, error) {
	if !config.IsEnabled() {
		return nil, fmt.Errorf("队列配置未启用")
	}

	service := &RabbitMQService{
		config:               config,
		taskHandlers:         make(map[string]types.TaskHandler),
		consumerRegistry:     NewConsumerRegistry(),
		restartChan:          make(chan string, 100), // 缓冲通道，避免阻塞
		stats:                &types.QueueStats{},
		maxReconnectAttempts: 10,
	}

	return service, nil
}

// startConsumerWithRetry 启动消费者并支持重试
func (r *RabbitMQService) startConsumerWithRetry(queueName string, handler types.MessageHandler, options types.ConsumeOptions) error {
	maxRetries := 3
	baseDelay := time.Second

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			delay := baseDelay * time.Duration(1<<uint(attempt-1)) // 指数退避
			logger.Debug("等待重试启动消费者", "queue", queueName, "attempt", attempt+1, "delay", delay)
			time.Sleep(delay)
		}

		// 检查连接状态
		if !r.IsConnected() {
			logger.Warn("连接未建立，跳过消费者启动", "queue", queueName, "attempt", attempt+1)
			continue
		}

		err := r.startSingleConsumer(queueName, handler, options)
		if err == nil {
			logger.Info("消费者启动成功", "queue", queueName, "attempt", attempt+1)
			return nil
		}

		logger.Error("消费者启动失败", "queue", queueName, "attempt", attempt+1, "error", err)

		// 更新消费者状态为错误
		r.consumerRegistry.UpdateStatus(queueName, ConsumerStatusError)
	}

	return fmt.Errorf("消费者启动失败，已达到最大重试次数: %d", maxRetries)
}

// startSingleConsumer 启动单个消费者
func (r *RabbitMQService) startSingleConsumer(queueName string, handler types.MessageHandler, options types.ConsumeOptions) error {
	// 更新消费者状态为启动中
	r.consumerRegistry.UpdateStatus(queueName, ConsumerStatusStarting)

	msgs, err := r.channel.Consume(
		queueName,
		options.Consumer,
		options.AutoAck,
		options.Exclusive,
		options.NoLocal,
		options.NoWait,
		amqp.Table(options.Arguments),
	)
	if err != nil {
		return fmt.Errorf("开始消费失败: %w", err)
	}

	// 获取消费者信息
	consumerInfo, exists := r.consumerRegistry.Get(queueName)
	if !exists {
		return fmt.Errorf("消费者信息未找到: %s", queueName)
	}

	// 更新消费者状态为运行中
	consumerInfo.UpdateStatus(ConsumerStatusRunning)

	logger.Info("开始消费队列", "queue", queueName, "consumer", options.Consumer)

	// 启动消费者goroutine
	r.consumerWg.Add(1)
	go r.runConsumer(queueName, msgs, handler, options.AutoAck, consumerInfo)

	return nil
}

// runConsumer 运行消费者goroutine
func (r *RabbitMQService) runConsumer(queueName string, msgs <-chan amqp.Delivery, handler types.MessageHandler, autoAck bool, consumerInfo *ConsumerInfo) {
	defer r.consumerWg.Done()
	defer func() {
		if rec := recover(); rec != nil {
			logger.Error("消费者goroutine panic", "queue", queueName, "panic", rec)
			consumerInfo.UpdateStatus(ConsumerStatusError)
			// 尝试重启消费者
			go r.scheduleConsumerRestart(queueName)
		}
	}()

	logger.Debug("消费者goroutine已启动", "queue", queueName)

	for {
		select {
		case msg, ok := <-msgs:
			if !ok {
				logger.Warn("消息通道已关闭", "queue", queueName)
				consumerInfo.UpdateStatus(ConsumerStatusStopped)
				// 尝试重启消费者
				go r.scheduleConsumerRestart(queueName)
				return
			}

			// 处理消息
			r.handleMessageWithTracking(consumerInfo.Context, msg, handler, autoAck, consumerInfo)

		case <-consumerInfo.Context.Done():
			logger.Info("停止消费队列", "queue", queueName)
			consumerInfo.UpdateStatus(ConsumerStatusStopped)
			return

		case <-r.ctx.Done():
			logger.Info("服务关闭，停止消费队列", "queue", queueName)
			consumerInfo.UpdateStatus(ConsumerStatusStopped)
			return
		}
	}
}

// scheduleConsumerRestart 调度消费者重启
func (r *RabbitMQService) scheduleConsumerRestart(queueName string) {
	select {
	case r.restartChan <- queueName:
		logger.Debug("已调度消费者重启", "queue", queueName)
	default:
		logger.Warn("重启通道已满，跳过重启调度", "queue", queueName)
	}
}

// monitorConsumers 监控消费者状态并处理重启请求
func (r *RabbitMQService) monitorConsumers() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case queueName := <-r.restartChan:
			logger.Info("处理消费者重启请求", "queue", queueName)
			r.handleConsumerRestart(queueName)

		case <-ticker.C:
			r.checkConsumerHealth()

		case <-r.ctx.Done():
			logger.Info("停止消费者监控")
			return
		}
	}
}

// handleConsumerRestart 处理消费者重启
func (r *RabbitMQService) handleConsumerRestart(queueName string) {
	consumerInfo, exists := r.consumerRegistry.Get(queueName)
	if !exists {
		logger.Warn("尝试重启不存在的消费者", "queue", queueName)
		return
	}

	// 检查连接状态
	if !r.IsConnected() {
		logger.Warn("连接未建立，延迟重启消费者", "queue", queueName)
		// 延迟重试
		time.AfterFunc(5*time.Second, func() {
			r.scheduleConsumerRestart(queueName)
		})
		return
	}

	// 更新状态为重启中
	consumerInfo.UpdateStatus(ConsumerStatusRestarting)

	// 启动消费者
	err := r.startConsumerWithRetry(queueName, consumerInfo.Handler, consumerInfo.Options)
	if err != nil {
		logger.Error("消费者重启失败", "queue", queueName, "error", err)
		consumerInfo.UpdateStatus(ConsumerStatusError)
		// 延迟重试
		time.AfterFunc(10*time.Second, func() {
			r.scheduleConsumerRestart(queueName)
		})
	} else {
		logger.Info("消费者重启成功", "queue", queueName)
	}
}

// checkConsumerHealth 检查消费者健康状态
func (r *RabbitMQService) checkConsumerHealth() {
	consumers := r.consumerRegistry.List()
	now := time.Now()

	for queueName, consumerInfo := range consumers {
		messageCount, errorCount, lastActivity, lastError := consumerInfo.GetStats()

		// 检查消费者是否长时间无活动（超过5分钟）
		if consumerInfo.GetStatus() == ConsumerStatusRunning && now.Sub(lastActivity) > 5*time.Minute {
			logger.Warn("消费者长时间无活动",
				"queue", queueName,
				"last_activity", lastActivity,
				"message_count", messageCount,
				"error_count", errorCount)
		}

		// 检查错误率是否过高
		if messageCount > 0 && errorCount > 0 {
			errorRate := float64(errorCount) / float64(messageCount)
			if errorRate > 0.1 { // 错误率超过10%
				logger.Warn("消费者错误率过高",
					"queue", queueName,
					"error_rate", errorRate,
					"message_count", messageCount,
					"error_count", errorCount,
					"last_error", lastError)
			}
		}
	}
}

// Connect 连接到 RabbitMQ
func (r *RabbitMQService) Connect(ctx context.Context) error {
	r.ctx, r.cancel = context.WithCancel(ctx)

	logger.Info("正在连接到 RabbitMQ...", "url", r.config.URL)

	// 强制IPv4连接：替换localhost为127.0.0.1
	connectionURL := r.config.URL
	if strings.Contains(connectionURL, "localhost") {
		connectionURL = strings.Replace(connectionURL, "localhost", "127.0.0.1", 1)
		logger.Debug("强制IPv4连接", "original_url", r.config.URL, "modified_url", connectionURL)
	}

	// 建立连接
	conn, err := amqp.Dial(connectionURL)
	if err != nil {
		return fmt.Errorf("连接 RabbitMQ 失败: %w", err)
	}

	// 创建通道
	ch, err := conn.Channel()
	if err != nil {
		conn.Close()
		return fmt.Errorf("创建通道失败: %w", err)
	}

	// 设置 QoS
	if err := ch.Qos(r.config.PrefetchCount, 0, false); err != nil {
		ch.Close()
		conn.Close()
		return fmt.Errorf("设置 QoS 失败: %w", err)
	}

	r.connection = conn
	r.channel = ch

	r.connMu.Lock()
	r.isConnected = true
	r.reconnectAttempts = 0
	r.connMu.Unlock()

	// 声明默认交换机
	if err := r.declareDefaultExchange(); err != nil {
		return fmt.Errorf("声明默认交换机失败: %w", err)
	}

	// 监听连接关闭
	go r.watchConnection()

	logger.Info("RabbitMQ 连接建立成功")
	return nil
}

// declareDefaultExchange 声明默认交换机
func (r *RabbitMQService) declareDefaultExchange() error {
	exchange := r.config.DefaultExchange

	return r.channel.ExchangeDeclare(
		exchange.Name,
		exchange.Type,
		exchange.Options.Durable,
		exchange.Options.AutoDelete,
		false, // internal
		false, // no-wait
		nil,   // arguments
	)
}

// watchConnection 监听连接状态
func (r *RabbitMQService) watchConnection() {
	closeChan := make(chan *amqp.Error)
	r.connection.NotifyClose(closeChan)

	select {
	case err := <-closeChan:
		if err != nil {
			logger.Warn("RabbitMQ 连接断开", "error", err)

			r.connMu.Lock()
			r.isConnected = false
			r.connMu.Unlock()

			// 尝试重连
			go r.attemptReconnect()
		}
	case <-r.ctx.Done():
		return
	}
}

// attemptReconnect 尝试重连
func (r *RabbitMQService) attemptReconnect() {
	r.connMu.Lock()
	if r.reconnectAttempts >= r.maxReconnectAttempts {
		r.connMu.Unlock()
		logger.Error("达到最大重连次数，停止重连", "attempts", r.reconnectAttempts)
		return
	}

	r.reconnectAttempts++
	attempts := r.reconnectAttempts
	r.connMu.Unlock()

	logger.Info("尝试重连 RabbitMQ", "attempt", attempts, "max", r.maxReconnectAttempts)

	// 指数退避延迟
	delay := r.config.ReconnectDelay * time.Duration(attempts)
	if delay > 60*time.Second {
		delay = 60 * time.Second // 最大延迟60秒
	}

	logger.Debug("等待重连延迟", "delay", delay, "attempt", attempts)
	select {
	case <-time.After(delay):
	case <-r.ctx.Done():
		logger.Info("重连被取消")
		return
	}

	// 尝试重新连接
	if err := r.Connect(r.ctx); err != nil {
		logger.Error("重连失败", "error", err, "attempt", attempts)
		// 递归尝试下一次重连
		go r.attemptReconnect()
	} else {
		logger.Info("✅ RabbitMQ 重连成功", "attempts", attempts)

		// 重连成功后，重启所有已注册的消费者
		consumerCount := r.consumerRegistry.Count()
		if consumerCount > 0 {
			logger.Info("重连成功，开始重启消费者", "consumer_count", consumerCount)
			go r.restartAllConsumers()
		} else {
			logger.Debug("重连成功，无需重启消费者")
		}
	}
}

// Close 关闭连接
func (r *RabbitMQService) Close() error {
	logger.Info("正在关闭 RabbitMQ 连接...")

	// 首先停止所有消费者
	r.stopAllConsumers()

	// 取消上下文，停止监控和重连
	if r.cancel != nil {
		r.cancel()
	}

	// 等待所有消费者goroutine结束
	logger.Debug("等待消费者goroutine结束...")
	r.consumerWg.Wait()

	r.connMu.Lock()
	r.isConnected = false
	r.connMu.Unlock()

	var err error
	if r.channel != nil {
		if closeErr := r.channel.Close(); closeErr != nil {
			err = closeErr
		}
	}

	if r.connection != nil {
		if closeErr := r.connection.Close(); closeErr != nil {
			err = closeErr
		}
	}

	logger.Info("RabbitMQ 连接已关闭")
	return err
}

// stopAllConsumers 停止所有消费者
func (r *RabbitMQService) stopAllConsumers() {
	consumers := r.consumerRegistry.List()
	logger.Info("正在停止所有消费者", "count", len(consumers))

	for queueName, consumerInfo := range consumers {
		logger.Debug("停止消费者", "queue", queueName)

		// 取消消费者上下文
		if consumerInfo.Cancel != nil {
			consumerInfo.Cancel()
		}

		// 更新状态
		consumerInfo.UpdateStatus(ConsumerStatusStopped)
	}

	// 清空消费者注册表
	r.consumerRegistry = NewConsumerRegistry()
}

// IsConnected 检查是否已连接
func (r *RabbitMQService) IsConnected() bool {
	r.connMu.RLock()
	defer r.connMu.RUnlock()
	return r.isConnected
}

// DeclareQueue 声明队列
func (r *RabbitMQService) DeclareQueue(ctx context.Context, name string, options types.QueueOptions) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	_, err := r.channel.QueueDeclare(
		name,
		options.Durable,
		options.AutoDelete,
		options.Exclusive,
		options.NoWait,
		amqp.Table(options.Arguments),
	)

	if err == nil {
		logger.Debug("队列声明成功", "name", name)
	}

	return err
}

// DeleteQueue 删除队列
func (r *RabbitMQService) DeleteQueue(ctx context.Context, name string, options types.DeleteQueueOptions) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	_, err := r.channel.QueueDelete(
		name,
		options.IfUnused,
		options.IfEmpty,
		options.NoWait,
	)

	if err == nil {
		logger.Debug("队列删除成功", "name", name)
	}

	return err
}

// DeclareExchange 声明交换机
func (r *RabbitMQService) DeclareExchange(ctx context.Context, name, kind string, options types.ExchangeOptions) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	err := r.channel.ExchangeDeclare(
		name,
		kind,
		options.Durable,
		options.AutoDelete,
		false, // internal
		false, // no-wait
		nil,   // arguments
	)

	if err == nil {
		logger.Debug("交换机声明成功", "name", name, "type", kind)
	}

	return err
}

// DeclareDelayExchange 声明延迟交换机（带参数）
func (r *RabbitMQService) DeclareDelayExchange(ctx context.Context, name string, arguments amqp.Table) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	err := r.channel.ExchangeDeclare(
		name,
		"x-delayed-message",
		true,  // durable
		false, // auto-delete
		false, // internal
		false, // no-wait
		arguments,
	)

	if err == nil {
		logger.Debug("延迟交换机声明成功", "name", name)
	}

	return err
}

// DeleteExchange 删除交换机
func (r *RabbitMQService) DeleteExchange(ctx context.Context, name string, options types.DeleteExchangeOptions) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	err := r.channel.ExchangeDelete(
		name,
		options.IfUnused,
		options.NoWait,
	)

	if err == nil {
		logger.Debug("交换机删除成功", "name", name)
	}

	return err
}

// BindQueue 绑定队列到交换机
func (r *RabbitMQService) BindQueue(ctx context.Context, queueName, exchangeName, routingKey string) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	err := r.channel.QueueBind(
		queueName,
		routingKey,
		exchangeName,
		false, // no-wait
		nil,   // arguments
	)

	if err == nil {
		logger.Debug("队列绑定成功", "queue", queueName, "exchange", exchangeName, "routing_key", routingKey)
	}

	return err
}

// UnbindQueue 解绑队列
func (r *RabbitMQService) UnbindQueue(ctx context.Context, queueName, exchangeName, routingKey string) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	err := r.channel.QueueUnbind(
		queueName,
		routingKey,
		exchangeName,
		nil, // arguments
	)

	if err == nil {
		logger.Debug("队列解绑成功", "queue", queueName, "exchange", exchangeName, "routing_key", routingKey)
	}

	return err
}

// Publish 发布消息
func (r *RabbitMQService) Publish(ctx context.Context, exchange, routingKey string, message []byte, options types.PublishOptions) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	// 构建消息属性
	headers := amqp.Table{}
	if options.Headers != nil {
		for k, v := range options.Headers {
			headers[k] = v
		}
	}

	publishing := amqp.Publishing{
		Headers:     headers,
		ContentType: "application/octet-stream",
		Body:        message,
		Priority:    options.Priority,
		Timestamp:   time.Now(),
	}

	// 设置 TTL
	if options.TTL > 0 {
		publishing.Expiration = fmt.Sprintf("%d", options.TTL.Milliseconds())
	}

	err := r.channel.PublishWithContext(
		ctx,
		exchange,
		routingKey,
		options.Mandatory,
		options.Immediate,
		publishing,
	)

	if err == nil {
		r.statsMu.Lock()
		r.stats.Published++
		r.stats.LastUpdated = time.Now()
		r.statsMu.Unlock()

		logger.Debug("消息发布成功", "exchange", exchange, "routing_key", routingKey, "size", len(message))
	}

	return err
}

// PublishJSON 发布 JSON 消息
func (r *RabbitMQService) PublishJSON(ctx context.Context, exchange, routingKey string, message interface{}, options types.PublishOptions) error {
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化 JSON 失败: %w", err)
	}

	// 设置内容类型
	if options.Headers == nil {
		options.Headers = make(map[string]interface{})
	}
	options.Headers["content-type"] = "application/json"

	return r.Publish(ctx, exchange, routingKey, data, options)
}

// Consume 消费消息（增强版本，支持自动重启和状态跟踪）
func (r *RabbitMQService) Consume(ctx context.Context, queueName string, handler types.MessageHandler, options types.ConsumeOptions) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	// 检查是否已有消费者在运行
	if existingConsumer, exists := r.consumerRegistry.Get(queueName); exists {
		if existingConsumer.GetStatus() == ConsumerStatusRunning {
			logger.Warn("队列已有消费者在运行", "queue", queueName)
			return fmt.Errorf("队列 %s 已有消费者在运行", queueName)
		}
		// 如果存在但未运行，先注销旧的消费者
		r.consumerRegistry.Unregister(queueName)
	}

	// 创建消费者上下文
	consumerCtx, consumerCancel := context.WithCancel(ctx)

	// 创建消费者信息
	consumerInfo := &ConsumerInfo{
		QueueName:    queueName,
		Handler:      handler,
		Options:      options,
		Context:      consumerCtx,
		Cancel:       consumerCancel,
		Status:       ConsumerStatusStopped,
		StartTime:    time.Now(),
		LastActivity: time.Now(),
		ErrorCount:   0,
		MessageCount: 0,
	}

	// 注册消费者
	r.consumerRegistry.Register(queueName, consumerInfo)

	// 启动消费者监控（如果尚未启动）
	r.startConsumerMonitorOnce()

	// 启动消费者
	err := r.startConsumerWithRetry(queueName, handler, options)
	if err != nil {
		r.consumerRegistry.Unregister(queueName)
		return fmt.Errorf("启动消费者失败: %w", err)
	}

	logger.Info("消费者注册并启动成功", "queue", queueName, "consumer", options.Consumer)
	return nil
}

// startConsumerMonitorOnce 确保消费者监控只启动一次
func (r *RabbitMQService) startConsumerMonitorOnce() {
	r.monitorMu.Lock()
	defer r.monitorMu.Unlock()

	if !r.monitorStarted {
		r.monitorStarted = true
		go r.monitorConsumers()
		logger.Info("消费者监控已启动")
	}
}

// restartAllConsumers 重启所有已注册的消费者
func (r *RabbitMQService) restartAllConsumers() {
	consumers := r.consumerRegistry.List()
	logger.Info("开始重启所有消费者", "count", len(consumers))

	for queueName, consumerInfo := range consumers {
		logger.Debug("重启消费者", "queue", queueName, "status", consumerInfo.GetStatus().String())

		// 更新状态为重启中
		consumerInfo.UpdateStatus(ConsumerStatusRestarting)

		// 异步重启消费者
		go func(qName string, cInfo *ConsumerInfo) {
			err := r.startConsumerWithRetry(qName, cInfo.Handler, cInfo.Options)
			if err != nil {
				logger.Error("重启消费者失败", "queue", qName, "error", err)
				cInfo.UpdateStatus(ConsumerStatusError)
			} else {
				logger.Info("重启消费者成功", "queue", qName)
			}
		}(queueName, consumerInfo)
	}
}

// GetConsumerStats 获取消费者统计信息
func (r *RabbitMQService) GetConsumerStats() map[string]map[string]interface{} {
	consumers := r.consumerRegistry.List()
	stats := make(map[string]map[string]interface{})

	for queueName, consumerInfo := range consumers {
		messageCount, errorCount, lastActivity, lastError := consumerInfo.GetStats()

		consumerStats := map[string]interface{}{
			"status":        consumerInfo.GetStatus().String(),
			"start_time":    consumerInfo.StartTime,
			"last_activity": lastActivity,
			"message_count": messageCount,
			"error_count":   errorCount,
			"uptime":        time.Since(consumerInfo.StartTime),
		}

		if lastError != nil {
			consumerStats["last_error"] = lastError.Error()
		}

		stats[queueName] = consumerStats
	}

	return stats
}

// GetConsumerCount 获取消费者数量
func (r *RabbitMQService) GetConsumerCount() int {
	return r.consumerRegistry.Count()
}

// GetConsumerStatus 获取指定队列的消费者状态
func (r *RabbitMQService) GetConsumerStatus(queueName string) (string, bool) {
	if consumerInfo, exists := r.consumerRegistry.Get(queueName); exists {
		return consumerInfo.GetStatus().String(), true
	}
	return "", false
}

// ListActiveConsumers 列出所有活跃的消费者
func (r *RabbitMQService) ListActiveConsumers() []string {
	consumers := r.consumerRegistry.List()
	var activeQueues []string

	for queueName, consumerInfo := range consumers {
		if consumerInfo.GetStatus() == ConsumerStatusRunning {
			activeQueues = append(activeQueues, queueName)
		}
	}

	return activeQueues
}

// RestartConsumer 手动重启指定的消费者
func (r *RabbitMQService) RestartConsumer(queueName string) error {
	_, exists := r.consumerRegistry.Get(queueName)
	if !exists {
		return fmt.Errorf("消费者不存在: %s", queueName)
	}

	logger.Info("手动重启消费者", "queue", queueName)
	r.scheduleConsumerRestart(queueName)
	return nil
}

// handleMessageWithTracking 处理单个消息并跟踪统计信息
func (r *RabbitMQService) handleMessageWithTracking(ctx context.Context, delivery amqp.Delivery, handler types.MessageHandler, autoAck bool, consumerInfo *ConsumerInfo) {
	defer func() {
		if rec := recover(); rec != nil {
			logger.Error("消息处理 panic", "panic", rec, "queue", consumerInfo.QueueName)
			consumerInfo.IncrementErrorCount(fmt.Errorf("panic: %v", rec))
			if !autoAck {
				delivery.Nack(false, true) // 重新入队
			}
		}
	}()

	// 构建消息对象
	message := &types.Message{
		ID:          delivery.MessageId,
		Body:        delivery.Body,
		Headers:     make(map[string]interface{}),
		ContentType: delivery.ContentType,
		Timestamp:   delivery.Timestamp,
		ReplyTo:     delivery.ReplyTo,
		Priority:    delivery.Priority,
	}

	// 转换头部
	for k, v := range delivery.Headers {
		message.Headers[k] = v
	}

	// 处理消息
	err := handler(ctx, message)

	if !autoAck {
		if err != nil {
			logger.Error("消息处理失败", "error", err, "message_id", delivery.MessageId, "queue", consumerInfo.QueueName)

			// 更新消费者错误统计
			consumerInfo.IncrementErrorCount(err)

			r.statsMu.Lock()
			r.stats.Failed++
			r.statsMu.Unlock()

			// 检查是否是网络相关错误
			if r.isNetworkError(err) {
				logger.Warn("检测到网络错误，可能需要重连", "error", err, "queue", consumerInfo.QueueName)
				// 对于网络错误，直接重新入队，不计入重试次数
				delivery.Nack(false, true)
				return
			}

			// 检查重试次数
			retryCount := r.getRetryCount(delivery.Headers)
			if retryCount < r.config.MaxRetries {
				// 更新重试计数并重新发布消息
				newRetryCount := retryCount + 1
				logger.Info("消息重新入队", "retry_count", newRetryCount, "message_id", delivery.MessageId, "queue", consumerInfo.QueueName)

				// 重新发布消息到队列，带有更新的重试计数
				if err := r.republishWithRetryCount(delivery, newRetryCount); err != nil {
					logger.Error("重新发布消息失败", "error", err, "message_id", delivery.MessageId, "queue", consumerInfo.QueueName)
					delivery.Nack(false, false) // 丢弃消息
				} else {
					delivery.Ack(false) // 确认原消息
					r.statsMu.Lock()
					r.stats.Requeued++
					r.statsMu.Unlock()
				}
			} else {
				logger.Error("消息达到最大重试次数，丢弃", "message_id", delivery.MessageId, "retry_count", retryCount, "queue", consumerInfo.QueueName)
				delivery.Nack(false, false) // 丢弃消息
			}
		} else {
			delivery.Ack(false) // 确认消息

			// 更新消费者成功统计
			consumerInfo.IncrementMessageCount()

			r.statsMu.Lock()
			r.stats.Consumed++
			r.stats.LastUpdated = time.Now()
			r.statsMu.Unlock()

			logger.Debug("消息处理成功", "message_id", delivery.MessageId, "queue", consumerInfo.QueueName)
		}
	} else {
		// 自动确认模式下也要更新统计
		if err != nil {
			consumerInfo.IncrementErrorCount(err)
		} else {
			consumerInfo.IncrementMessageCount()
		}
	}
}

// isNetworkError 判断是否为网络相关错误
func (r *RabbitMQService) isNetworkError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	networkErrorKeywords := []string{
		"connection", "network", "timeout", "broken pipe",
		"connection reset", "no route to host", "connection refused",
	}

	for _, keyword := range networkErrorKeywords {
		if strings.Contains(strings.ToLower(errStr), keyword) {
			return true
		}
	}

	return false
}

// handleMessage 处理单个消息
func (r *RabbitMQService) handleMessage(ctx context.Context, delivery amqp.Delivery, handler types.MessageHandler, autoAck bool) {
	defer func() {
		if rec := recover(); rec != nil {
			logger.Error("消息处理 panic", "panic", rec)
			if !autoAck {
				delivery.Nack(false, true) // 重新入队
			}
		}
	}()

	// 构建消息对象
	message := &types.Message{
		ID:          delivery.MessageId,
		Body:        delivery.Body,
		Headers:     make(map[string]interface{}),
		ContentType: delivery.ContentType,
		Timestamp:   delivery.Timestamp,
		ReplyTo:     delivery.ReplyTo,
		Priority:    delivery.Priority,
	}

	// 转换头部
	for k, v := range delivery.Headers {
		message.Headers[k] = v
	}

	// 处理消息
	err := handler(ctx, message)

	if !autoAck {
		if err != nil {
			logger.Error("消息处理失败", "error", err, "message_id", delivery.MessageId)

			r.statsMu.Lock()
			r.stats.Failed++
			r.statsMu.Unlock()

			// 检查重试次数
			retryCount := r.getRetryCount(delivery.Headers)
			if retryCount < r.config.MaxRetries {
				// 更新重试计数并重新发布消息
				newRetryCount := retryCount + 1
				logger.Info("消息重新入队", "retry_count", newRetryCount, "message_id", delivery.MessageId)

				// 重新发布消息到队列，带有更新的重试计数
				if err := r.republishWithRetryCount(delivery, newRetryCount); err != nil {
					logger.Error("重新发布消息失败", "error", err, "message_id", delivery.MessageId)
					delivery.Nack(false, false) // 丢弃消息
				} else {
					delivery.Ack(false) // 确认原消息
					r.statsMu.Lock()
					r.stats.Requeued++
					r.statsMu.Unlock()
				}
			} else {
				logger.Error("消息达到最大重试次数，丢弃", "message_id", delivery.MessageId, "retry_count", retryCount)
				delivery.Nack(false, false) // 丢弃消息
			}
		} else {
			delivery.Ack(false) // 确认消息

			r.statsMu.Lock()
			r.stats.Consumed++
			r.stats.LastUpdated = time.Now()
			r.statsMu.Unlock()

			logger.Debug("消息处理成功", "message_id", delivery.MessageId)
		}
	}
}

// getRetryCount 获取重试次数
func (r *RabbitMQService) getRetryCount(headers amqp.Table) int {
	if headers == nil {
		return 0
	}

	if count, ok := headers["x-retry-count"]; ok {
		if retryCount, ok := count.(int); ok {
			return retryCount
		}
	}

	return 0
}

// republishWithRetryCount 重新发布消息并更新重试计数
func (r *RabbitMQService) republishWithRetryCount(delivery amqp.Delivery, retryCount int) error {
	// 创建新的headers，包含更新的重试计数
	headers := amqp.Table{}
	if delivery.Headers != nil {
		for k, v := range delivery.Headers {
			headers[k] = v
		}
	}
	headers["x-retry-count"] = retryCount

	// 重新发布消息
	publishing := amqp.Publishing{
		Headers:         headers,
		ContentType:     delivery.ContentType,
		ContentEncoding: delivery.ContentEncoding,
		Body:            delivery.Body,
		DeliveryMode:    delivery.DeliveryMode,
		Priority:        delivery.Priority,
		CorrelationId:   delivery.CorrelationId,
		ReplyTo:         delivery.ReplyTo,
		Expiration:      delivery.Expiration,
		MessageId:       delivery.MessageId,
		Timestamp:       delivery.Timestamp,
		Type:            delivery.Type,
		UserId:          delivery.UserId,
		AppId:           delivery.AppId,
	}

	return r.channel.PublishWithContext(
		context.Background(),
		delivery.Exchange,
		delivery.RoutingKey,
		false, // mandatory
		false, // immediate
		publishing,
	)
}

// PublishTask 发布任务
func (r *RabbitMQService) PublishTask(ctx context.Context, queueName, taskType string, data interface{}, options types.PublishOptions) (string, error) {
	// 首先确保队列存在
	queueOptions := types.QueueOptions{
		Durable:    true,  // 持久化队列
		AutoDelete: false, // 不自动删除
		Exclusive:  false, // 非独占
		NoWait:     false, // 等待确认
		Arguments:  nil,   // 无额外参数
	}

	if err := r.DeclareQueue(ctx, queueName, queueOptions); err != nil {
		logger.Error("声明队列失败", "queue", queueName, "error", err)
		return "", fmt.Errorf("声明队列失败: %w", err)
	}

	// 绑定队列到默认交换机
	exchangeName := r.config.DefaultExchange.Name
	routingKey := queueName // 使用队列名作为路由键

	if err := r.BindQueue(ctx, queueName, exchangeName, routingKey); err != nil {
		logger.Error("绑定队列到交换机失败", "queue", queueName, "exchange", exchangeName, "error", err)
		return "", fmt.Errorf("绑定队列到交换机失败: %w", err)
	}

	// 构建任务消息
	task := map[string]interface{}{
		"id":        uuid.New().String(),
		"type":      taskType,
		"data":      data,
		"timestamp": time.Now().Unix(),
	}

	// 发布任务到交换机（重用之前声明的变量）
	err := r.PublishJSON(ctx, exchangeName, routingKey, task, options)
	if err != nil {
		return "", err
	}

	logger.Debug("任务发布成功", "queue", queueName, "task_type", taskType, "task_id", task["id"])
	return task["id"].(string), nil
}

// PublishDelayedTask 发布延迟任务
func (r *RabbitMQService) PublishDelayedTask(ctx context.Context, queueName, taskType string, data interface{}, delay time.Duration, options types.PublishOptions) (string, error) {
	// 首先确保延迟交换机存在
	delayExchangeName := "zeka.delay"
	arguments := amqp.Table{
		"x-delayed-type": "direct",
	}

	if err := r.DeclareDelayExchange(ctx, delayExchangeName, arguments); err != nil {
		logger.Error("声明延迟交换机失败", "exchange", delayExchangeName, "error", err)
		return "", fmt.Errorf("声明延迟交换机失败: %w", err)
	}

	// 确保目标队列存在
	queueOptions := types.QueueOptions{
		Durable:    true,
		AutoDelete: false,
		Exclusive:  false,
		NoWait:     false,
		Arguments:  nil,
	}

	if err := r.DeclareQueue(ctx, queueName, queueOptions); err != nil {
		logger.Error("声明队列失败", "queue", queueName, "error", err)
		return "", fmt.Errorf("声明队列失败: %w", err)
	}

	// 绑定队列到延迟交换机
	routingKey := queueName
	if err := r.BindQueue(ctx, queueName, delayExchangeName, routingKey); err != nil {
		logger.Error("绑定队列到延迟交换机失败", "queue", queueName, "exchange", delayExchangeName, "error", err)
		return "", fmt.Errorf("绑定队列到延迟交换机失败: %w", err)
	}

	// 构建任务消息
	task := map[string]interface{}{
		"id":        uuid.New().String(),
		"type":      taskType,
		"data":      data,
		"timestamp": time.Now().Unix(),
		"delay":     delay.String(),
	}

	// 设置延迟头部
	if options.Headers == nil {
		options.Headers = make(map[string]interface{})
	}
	options.Headers["x-delay"] = delay.Milliseconds()

	// 发布延迟任务到延迟交换机
	err := r.PublishJSON(ctx, delayExchangeName, routingKey, task, options)
	if err != nil {
		return "", fmt.Errorf("发布延迟任务失败: %w", err)
	}

	logger.Debug("延迟任务发布成功", "queue", queueName, "task_type", taskType, "task_id", task["id"], "delay", delay)
	return task["id"].(string), nil
}

// RegisterTaskHandler 注册任务处理器
func (r *RabbitMQService) RegisterTaskHandler(taskType string, handler types.TaskHandler) {
	r.handlerMu.Lock()
	defer r.handlerMu.Unlock()

	r.taskHandlers[taskType] = handler
	logger.Debug("注册任务处理器", "type", taskType)
}

// StartTaskConsumer 启动任务消费者
func (r *RabbitMQService) StartTaskConsumer(ctx context.Context, queueName string, options types.ConsumeOptions) error {
	// 创建任务消息处理器
	taskHandler := func(ctx context.Context, message *types.Message) error {
		// 解析任务
		var task map[string]interface{}
		if err := json.Unmarshal(message.Body, &task); err != nil {
			return fmt.Errorf("解析任务失败: %w", err)
		}

		taskType, ok := task["type"].(string)
		if !ok {
			return fmt.Errorf("任务类型无效")
		}

		// 获取处理器
		r.handlerMu.RLock()
		handler, exists := r.taskHandlers[taskType]
		r.handlerMu.RUnlock()

		if !exists {
			return fmt.Errorf("未找到任务处理器: %s", taskType)
		}

		// 执行任务
		return handler(ctx, message.Body)
	}

	return r.Consume(ctx, queueName, taskHandler, options)
}

// GetQueueInfo 获取队列信息
func (r *RabbitMQService) GetQueueInfo(ctx context.Context, queueName string) (*types.QueueInfo, error) {
	if !r.IsConnected() {
		return nil, types.ErrQueueNotAvailable
	}

	queue, err := r.channel.QueueInspect(queueName)
	if err != nil {
		return nil, err
	}

	return &types.QueueInfo{
		Name:       queue.Name,
		Messages:   queue.Messages,
		Consumers:  queue.Consumers,
		Durable:    true, // 假设为持久化队列
		AutoDelete: false,
		Exclusive:  false,
		Arguments:  nil,
	}, nil
}

// GetStats 获取统计信息
func (r *RabbitMQService) GetStats() *types.QueueStats {
	r.statsMu.RLock()
	defer r.statsMu.RUnlock()

	// 返回副本
	return &types.QueueStats{
		Published:   r.stats.Published,
		Consumed:    r.stats.Consumed,
		Failed:      r.stats.Failed,
		Requeued:    r.stats.Requeued,
		LastUpdated: r.stats.LastUpdated,
	}
}

// GetHealthStatus 获取队列服务健康状态
func (r *RabbitMQService) GetHealthStatus() *types.QueueHealthStatus {
	r.connMu.RLock()
	connected := r.isConnected
	attempts := r.reconnectAttempts
	r.connMu.RUnlock()

	r.statsMu.RLock()
	stats := *r.stats
	r.statsMu.RUnlock()

	// 收集消费者健康信息
	consumers := r.consumerRegistry.List()
	consumerHealthInfo := make(map[string]types.ConsumerHealthInfo)

	healthyConsumers := 0
	unhealthyConsumers := 0
	stoppedConsumers := 0

	for queueName, consumerInfo := range consumers {
		messageCount, errorCount, lastActivity, lastError := consumerInfo.GetStats()
		status := consumerInfo.GetStatus()

		// 计算错误率
		var errorRate float64
		if messageCount > 0 {
			errorRate = float64(errorCount) / float64(messageCount)
		}

		// 计算运行时间
		uptime := time.Since(consumerInfo.StartTime)

		healthInfo := types.ConsumerHealthInfo{
			Status:       status.String(),
			StartTime:    consumerInfo.StartTime,
			LastActivity: lastActivity,
			MessageCount: messageCount,
			ErrorCount:   errorCount,
			ErrorRate:    errorRate,
			Uptime:       uptime.String(),
		}

		if lastError != nil {
			healthInfo.LastError = lastError.Error()
		}

		consumerHealthInfo[queueName] = healthInfo

		// 统计消费者状态
		switch status {
		case ConsumerStatusRunning:
			if errorRate > 0.1 || time.Since(lastActivity) > 10*time.Minute {
				unhealthyConsumers++
			} else {
				healthyConsumers++
			}
		case ConsumerStatusStopped, ConsumerStatusError:
			stoppedConsumers++
		default:
			unhealthyConsumers++
		}
	}

	status := &types.QueueHealthStatus{
		Connected:         connected,
		URL:               r.config.URL,
		ReconnectAttempts: attempts,
		MaxRetries:        r.maxReconnectAttempts,
		Stats:             stats,
		LastCheck:         time.Now(),
		Consumers:         consumerHealthInfo,
	}

	// 确定整体健康状态
	if !connected {
		status.Status = "unhealthy"
		if attempts >= r.maxReconnectAttempts {
			status.Message = "RabbitMQ 连接失败，已达到最大重连次数"
		} else {
			status.Message = fmt.Sprintf("RabbitMQ 连接失败，正在重连 (尝试 %d/%d)", attempts, r.maxReconnectAttempts)
		}
	} else if stoppedConsumers > 0 {
		status.Status = "degraded"
		status.Message = fmt.Sprintf("RabbitMQ 连接正常，但有 %d 个消费者停止运行", stoppedConsumers)
	} else if unhealthyConsumers > 0 {
		status.Status = "degraded"
		status.Message = fmt.Sprintf("RabbitMQ 连接正常，但有 %d 个消费者状态异常", unhealthyConsumers)
	} else {
		status.Status = "healthy"
		if len(consumers) > 0 {
			status.Message = fmt.Sprintf("RabbitMQ 连接正常，%d 个消费者运行正常", healthyConsumers)
		} else {
			status.Message = "RabbitMQ 连接正常，无活跃消费者"
		}
	}

	return status
}

// PublishBatch 批量发布消息
func (r *RabbitMQService) PublishBatch(ctx context.Context, exchange string, messages []types.BatchMessage) error {
	r.connMu.RLock()
	connected := r.isConnected
	r.connMu.RUnlock()

	if !connected {
		return fmt.Errorf("RabbitMQ 连接未建立")
	}

	ch := r.channel
	if ch == nil {
		return fmt.Errorf("RabbitMQ 频道未建立")
	}

	// 批量发布消息（不使用事务，简化实现）
	for _, msg := range messages {
		publishing := amqp.Publishing{
			ContentType:  "application/json",
			Body:         msg.Body,
			Priority:     msg.Options.Priority,
			DeliveryMode: amqp.Persistent,
			Timestamp:    time.Now(),
		}

		if msg.Options.TTL > 0 {
			publishing.Expiration = fmt.Sprintf("%d", msg.Options.TTL.Milliseconds())
		}

		if err := ch.PublishWithContext(ctx, exchange, msg.RoutingKey, false, false, publishing); err != nil {
			return fmt.Errorf("批量发布消息失败: %w", err)
		}
	}

	// 更新统计
	r.statsMu.Lock()
	r.stats.Published += int64(len(messages))
	r.statsMu.Unlock()

	logger.Info("批量发布消息成功", "count", len(messages), "exchange", exchange)
	return nil
}

// SetupDeadLetterQueue 设置死信队列
func (r *RabbitMQService) SetupDeadLetterQueue(queueName string) error {
	r.connMu.RLock()
	connected := r.isConnected
	r.connMu.RUnlock()

	if !connected {
		return fmt.Errorf("RabbitMQ 连接未建立")
	}

	ch := r.channel
	if ch == nil {
		return fmt.Errorf("RabbitMQ 频道未建立")
	}

	// 创建死信交换机
	dlxName := queueName + ".dlx"
	if err := ch.ExchangeDeclare(dlxName, "direct", true, false, false, false, nil); err != nil {
		return fmt.Errorf("创建死信交换机失败: %w", err)
	}

	// 创建死信队列
	dlqName := queueName + ".dlq"
	_, err := ch.QueueDeclare(dlqName, true, false, false, false, nil)
	if err != nil {
		return fmt.Errorf("创建死信队列失败: %w", err)
	}

	// 绑定死信队列到死信交换机
	if err := ch.QueueBind(dlqName, queueName, dlxName, false, nil); err != nil {
		return fmt.Errorf("绑定死信队列失败: %w", err)
	}

	logger.Info("死信队列设置完成", "queue", queueName, "dlq", dlqName)
	return nil
}

// PurgeQueue 清空队列
func (r *RabbitMQService) PurgeQueue(queueName string) (int, error) {
	r.connMu.RLock()
	connected := r.isConnected
	r.connMu.RUnlock()

	if !connected {
		return 0, fmt.Errorf("RabbitMQ 连接未建立")
	}

	ch := r.channel
	if ch == nil {
		return 0, fmt.Errorf("RabbitMQ 频道未建立")
	}

	count, err := ch.QueuePurge(queueName, false)
	if err != nil {
		return 0, fmt.Errorf("清空队列失败: %w", err)
	}

	logger.Info("队列已清空", "queue", queueName, "count", count)
	return count, nil
}

// 简化的处理器映射（用于兼容现有代码）
var simpleHandlers = make(map[string]func(data interface{}) error)
var simpleHandlersMu sync.RWMutex

// RegisterHandler 注册任务处理器（简化版本，兼容现有代码）
func (r *RabbitMQService) RegisterHandler(taskType string, handler func(data interface{}) error) {
	simpleHandlersMu.Lock()
	defer simpleHandlersMu.Unlock()

	simpleHandlers[taskType] = handler
	logger.Debug("注册简化任务处理器", "type", taskType)
}

// StartConsumer 启动队列消费者（简化版本，兼容现有代码）
func (r *RabbitMQService) StartConsumer(queueName string, options interface{}) error {
	if !r.IsConnected() {
		return types.ErrQueueNotAvailable
	}

	logger.Info("启动简化队列消费者", "queue", queueName)

	// 首先声明队列（确保队列存在）
	queueOptions := types.QueueOptions{
		Durable:    true,  // 持久化队列
		AutoDelete: false, // 不自动删除
		Exclusive:  false, // 非独占
		NoWait:     false, // 等待确认
		Arguments:  nil,   // 无额外参数
	}

	if err := r.DeclareQueue(context.Background(), queueName, queueOptions); err != nil {
		logger.Error("声明队列失败", "queue", queueName, "error", err)
		return fmt.Errorf("声明队列失败: %w", err)
	}

	logger.Debug("队列声明成功", "queue", queueName)

	// 绑定队列到默认交换机
	exchangeName := r.config.DefaultExchange.Name
	routingKey := queueName // 使用队列名作为路由键

	if err := r.BindQueue(context.Background(), queueName, exchangeName, routingKey); err != nil {
		logger.Error("绑定队列到交换机失败", "queue", queueName, "exchange", exchangeName, "error", err)
		return fmt.Errorf("绑定队列到交换机失败: %w", err)
	}

	logger.Debug("队列绑定成功", "queue", queueName, "exchange", exchangeName, "routing_key", routingKey)

	// 使用默认的消费选项
	consumeOptions := types.ConsumeOptions{
		AutoAck:   false,
		Exclusive: false,
		NoLocal:   false,
		NoWait:    false,
	}

	// 创建消费者处理函数
	handler := func(ctx context.Context, message *types.Message) error {
		// 尝试解析消息
		var messageData map[string]interface{}
		if err := json.Unmarshal(message.Body, &messageData); err != nil {
			logger.Error("解析消息失败", "error", err)
			return err
		}

		// 获取任务类型
		taskType, ok := messageData["type"].(string)
		if !ok {
			taskType = queueName // 使用队列名作为默认任务类型
		}

		// 查找处理器
		simpleHandlersMu.RLock()
		taskHandler, exists := simpleHandlers[taskType]
		simpleHandlersMu.RUnlock()

		if !exists {
			logger.Warn("未找到简化任务处理器", "type", taskType)
			return fmt.Errorf("未找到任务处理器: %s", taskType)
		}

		// 执行处理器
		return taskHandler(messageData)
	}

	return r.Consume(context.Background(), queueName, handler, consumeOptions)
}

// NewConnectionPool 创建连接池
func NewConnectionPool(config types.QueueConfig, size int) (*ConnectionPool, error) {
	pool := &ConnectionPool{
		connections: make([]*amqp.Connection, 0, size),
		channels:    make([]*amqp.Channel, 0, size),
		size:        size,
		current:     0,
	}

	// 创建连接池
	for i := 0; i < size; i++ {
		conn, err := amqp.Dial(config.URL)
		if err != nil {
			// 清理已创建的连接
			pool.Close()
			return nil, fmt.Errorf("创建连接池连接失败: %w", err)
		}

		ch, err := conn.Channel()
		if err != nil {
			conn.Close()
			pool.Close()
			return nil, fmt.Errorf("创建连接池通道失败: %w", err)
		}

		// 设置 QoS
		if err := ch.Qos(config.PrefetchCount, 0, false); err != nil {
			ch.Close()
			conn.Close()
			pool.Close()
			return nil, fmt.Errorf("设置连接池 QoS 失败: %w", err)
		}

		pool.connections = append(pool.connections, conn)
		pool.channels = append(pool.channels, ch)
	}

	return pool, nil
}

// GetChannel 从连接池获取通道
func (p *ConnectionPool) GetChannel() *amqp.Channel {
	p.mu.Lock()
	defer p.mu.Unlock()

	if len(p.channels) == 0 {
		return nil
	}

	// 轮询获取通道
	ch := p.channels[p.current]
	p.current = (p.current + 1) % len(p.channels)

	return ch
}

// GetConnection 从连接池获取连接
func (p *ConnectionPool) GetConnection() *amqp.Connection {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if len(p.connections) == 0 {
		return nil
	}

	// 轮询获取连接
	conn := p.connections[p.current%len(p.connections)]
	return conn
}

// Close 关闭连接池
func (p *ConnectionPool) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	var errors []error

	// 关闭所有通道
	for _, ch := range p.channels {
		if ch != nil && !ch.IsClosed() {
			if err := ch.Close(); err != nil {
				errors = append(errors, err)
			}
		}
	}

	// 关闭所有连接
	for _, conn := range p.connections {
		if conn != nil && !conn.IsClosed() {
			if err := conn.Close(); err != nil {
				errors = append(errors, err)
			}
		}
	}

	p.connections = nil
	p.channels = nil

	if len(errors) > 0 {
		return fmt.Errorf("关闭连接池时出现错误: %v", errors)
	}

	return nil
}

// IsHealthy 检查连接池健康状态
func (p *ConnectionPool) IsHealthy() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()

	for _, conn := range p.connections {
		if conn == nil || conn.IsClosed() {
			return false
		}
	}

	for _, ch := range p.channels {
		if ch == nil || ch.IsClosed() {
			return false
		}
	}

	return true
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(service *RabbitMQService, interval time.Duration) *HealthChecker {
	return &HealthChecker{
		service:  service,
		interval: interval,
		stopCh:   make(chan struct{}),
	}
}

// Start 启动健康检查
func (hc *HealthChecker) Start(ctx context.Context) {
	ticker := time.NewTicker(hc.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-hc.stopCh:
			return
		case <-ticker.C:
			hc.checkHealth()
		}
	}
}

// Stop 停止健康检查
func (hc *HealthChecker) Stop() {
	close(hc.stopCh)
}

// checkHealth 执行健康检查
func (hc *HealthChecker) checkHealth() {
	// 检查主连接
	if hc.service.connection == nil || hc.service.connection.IsClosed() {
		logger.Warn("RabbitMQ主连接不健康，尝试重连")
		hc.service.attemptReconnect()
		return
	}

	// 检查主通道
	if hc.service.channel == nil || hc.service.channel.IsClosed() {
		logger.Warn("RabbitMQ主通道不健康，尝试重建")
		if err := hc.service.recreateChannel(); err != nil {
			logger.Error("RabbitMQ重建通道失败", "error", err)
		}
		return
	}

	// 检查连接池（如果存在）
	if hc.service.pool != nil && !hc.service.pool.IsHealthy() {
		logger.Warn("RabbitMQ连接池不健康")
		// 这里可以添加连接池重建逻辑
	}

	// 检查消费者状态
	hc.checkConsumerHealth()

	// 执行简单的健康检查操作
	if err := hc.service.channel.ExchangeDeclarePassive(
		"amq.direct", // 使用默认交换机进行测试
		"direct",
		true,
		false,
		false,
		false,
		nil,
	); err != nil {
		logger.Warn("RabbitMQ健康检查失败", "error", err)
	}
}

// checkConsumerHealth 检查消费者健康状态
func (hc *HealthChecker) checkConsumerHealth() {
	consumers := hc.service.consumerRegistry.List()
	now := time.Now()

	unhealthyConsumers := 0
	stoppedConsumers := 0

	for queueName, consumerInfo := range consumers {
		status := consumerInfo.GetStatus()
		messageCount, errorCount, lastActivity, lastError := consumerInfo.GetStats()

		switch status {
		case ConsumerStatusStopped, ConsumerStatusError:
			stoppedConsumers++
			logger.Warn("发现停止的消费者",
				"queue", queueName,
				"status", status.String(),
				"last_error", lastError)

			// 尝试重启停止的消费者
			go hc.service.scheduleConsumerRestart(queueName)

		case ConsumerStatusRunning:
			// 检查消费者是否长时间无活动
			if now.Sub(lastActivity) > 10*time.Minute {
				unhealthyConsumers++
				logger.Warn("消费者长时间无活动",
					"queue", queueName,
					"last_activity", lastActivity,
					"inactive_duration", now.Sub(lastActivity))
			}

			// 检查错误率
			if messageCount > 10 && errorCount > 0 {
				errorRate := float64(errorCount) / float64(messageCount)
				if errorRate > 0.2 { // 错误率超过20%
					unhealthyConsumers++
					logger.Warn("消费者错误率过高",
						"queue", queueName,
						"error_rate", errorRate,
						"message_count", messageCount,
						"error_count", errorCount)
				}
			}
		}
	}

	// 记录健康检查结果
	totalConsumers := len(consumers)
	if totalConsumers > 0 {
		healthyConsumers := totalConsumers - unhealthyConsumers - stoppedConsumers
		logger.Debug("消费者健康检查完成",
			"total", totalConsumers,
			"healthy", healthyConsumers,
			"unhealthy", unhealthyConsumers,
			"stopped", stoppedConsumers)

		if stoppedConsumers > 0 || unhealthyConsumers > totalConsumers/2 {
			logger.Warn("消费者健康状态异常",
				"stopped_consumers", stoppedConsumers,
				"unhealthy_consumers", unhealthyConsumers,
				"total_consumers", totalConsumers)
		}
	}
}

// recreateChannel 重建通道
func (r *RabbitMQService) recreateChannel() error {
	r.connMu.Lock()
	defer r.connMu.Unlock()

	if r.connection == nil || r.connection.IsClosed() {
		return fmt.Errorf("连接不可用，无法重建通道")
	}

	// 关闭旧通道
	if r.channel != nil && !r.channel.IsClosed() {
		r.channel.Close()
	}

	// 创建新通道
	ch, err := r.connection.Channel()
	if err != nil {
		return fmt.Errorf("重建通道失败: %w", err)
	}

	// 设置 QoS
	if err := ch.Qos(r.config.PrefetchCount, 0, false); err != nil {
		ch.Close()
		return fmt.Errorf("重建通道设置 QoS 失败: %w", err)
	}

	r.channel = ch
	logger.Info("RabbitMQ通道重建成功")

	return nil
}

// InitializePool 初始化连接池
func (r *RabbitMQService) InitializePool(poolSize int) error {
	if poolSize <= 0 {
		poolSize = 5 // 默认连接池大小
	}

	pool, err := NewConnectionPool(r.config, poolSize)
	if err != nil {
		return fmt.Errorf("初始化连接池失败: %w", err)
	}

	r.pool = pool
	logger.Info("RabbitMQ连接池初始化成功", "size", poolSize)

	return nil
}

// StartHealthChecker 启动健康检查
func (r *RabbitMQService) StartHealthChecker(ctx context.Context) {
	if r.healthChecker == nil {
		r.healthChecker = NewHealthChecker(r, 30*time.Second)
	}

	go r.healthChecker.Start(ctx)
	logger.Info("RabbitMQ健康检查器已启动")
}

// StopHealthChecker 停止健康检查
func (r *RabbitMQService) StopHealthChecker() {
	if r.healthChecker != nil {
		r.healthChecker.Stop()
		logger.Info("RabbitMQ健康检查器已停止")
	}
}

# 消息队列消费者增强功能

## 概述

本文档描述了对RabbitMQ消息队列服务的增强功能，主要解决"系统运行一段时间后，消息队列无法接收新消息，需要重启应用才能恢复正常"的问题。

## 问题分析

### 原有问题
1. **消费者goroutine生命周期管理缺陷**：消息通道关闭时，消费者goroutine直接退出，没有重启机制
2. **连接重连后消费者未重启**：重连成功后只重建了连接和通道，但没有重启消费者
3. **消费者状态跟踪缺失**：无法跟踪哪些队列有活跃的消费者
4. **健康检查不完整**：只检查连接状态，不检查消费者状态
5. **错误处理和恢复机制不足**：缺少指数退避重试和错误分类

## 解决方案

### 1. 消费者状态跟踪

#### 新增数据结构
```go
// ConsumerStatus 消费者状态
type ConsumerStatus int

const (
    ConsumerStatusStopped ConsumerStatus = iota
    ConsumerStatusStarting
    ConsumerStatusRunning
    ConsumerStatusError
    ConsumerStatusRestarting
)

// ConsumerInfo 消费者信息
type ConsumerInfo struct {
    QueueName    string
    Handler      types.MessageHandler
    Options      types.ConsumeOptions
    Context      context.Context
    Cancel       context.CancelFunc
    Status       ConsumerStatus
    StartTime    time.Time
    LastActivity time.Time
    ErrorCount   int64
    MessageCount int64
    LastError    error
    mu           sync.RWMutex
}

// ConsumerRegistry 消费者注册表
type ConsumerRegistry struct {
    consumers map[string]*ConsumerInfo
    mu        sync.RWMutex
}
```

#### 功能特性
- **状态跟踪**：实时跟踪每个消费者的状态（停止、启动中、运行中、错误、重启中）
- **统计信息**：记录消息处理数量、错误数量、最后活动时间等
- **线程安全**：所有操作都是线程安全的

### 2. 自动重启机制

#### 核心功能
- **消费者监控**：持续监控消费者状态，检测异常退出
- **自动重启**：消费者异常退出时自动重启
- **连接重连后恢复**：连接重连成功后自动重启所有已注册的消费者
- **指数退避重试**：使用指数退避算法进行重试，避免频繁重试

#### 关键方法
```go
func (r *RabbitMQService) startConsumerWithRetry(queueName string, handler types.MessageHandler, options types.ConsumeOptions) error
func (r *RabbitMQService) monitorConsumers()
func (r *RabbitMQService) restartAllConsumers()
func (r *RabbitMQService) handleConsumerRestart(queueName string)
```

### 3. 增强健康检查

#### 扩展功能
- **消费者状态检查**：检查每个消费者的运行状态
- **性能监控**：监控消息处理速度、错误率等指标
- **异常检测**：检测长时间无活动、高错误率等异常情况
- **详细报告**：提供包含消费者信息的详细健康报告

#### 健康状态类型
```go
type QueueHealthStatus struct {
    Status            string                        `json:"status"`             // "healthy", "unhealthy", "degraded"
    Message           string                        `json:"message"`            // 状态描述
    Connected         bool                          `json:"connected"`          // 是否连接
    Consumers         map[string]ConsumerHealthInfo `json:"consumers"`          // 消费者健康信息
    // ... 其他字段
}

type ConsumerHealthInfo struct {
    Status       string    `json:"status"`        // 消费者状态
    StartTime    time.Time `json:"start_time"`    // 启动时间
    LastActivity time.Time `json:"last_activity"` // 最后活动时间
    MessageCount int64     `json:"message_count"` // 处理消息数
    ErrorCount   int64     `json:"error_count"`   // 错误数
    ErrorRate    float64   `json:"error_rate"`    // 错误率
    Uptime       string    `json:"uptime"`        // 运行时间
    LastError    string    `json:"last_error"`    // 最后错误信息
}
```

### 4. 改进错误处理

#### 错误分类
- **网络错误**：连接断开、超时等，自动重试
- **业务错误**：消息处理逻辑错误，按重试策略处理

#### 重试机制
- **指数退避**：重试间隔逐渐增加
- **最大重试次数**：避免无限重试
- **错误统计**：记录错误次数和类型

## API 接口

### 新增方法

```go
// 消费者管理
func (r *RabbitMQService) GetConsumerCount() int
func (r *RabbitMQService) GetConsumerStatus(queueName string) (string, bool)
func (r *RabbitMQService) ListActiveConsumers() []string
func (r *RabbitMQService) RestartConsumer(queueName string) error
func (r *RabbitMQService) GetConsumerStats() map[string]map[string]interface{}

// 增强的健康检查
func (r *RabbitMQService) GetHealthStatus() *types.QueueHealthStatus
```

### 兼容性

所有现有的API接口保持不变，确保向后兼容：
- `Consume(ctx context.Context, queueName string, handler types.MessageHandler, options types.ConsumeOptions) error`
- `StartConsumer(queueName string, options interface{}) error`
- `RegisterHandler(taskType string, handler func(data interface{}) error)`

## 使用示例

### 基本使用（与之前相同）
```go
// 创建队列服务
queueService, err := queue.NewRabbitMQService(config)
if err != nil {
    log.Fatal(err)
}

// 连接到RabbitMQ
err = queueService.Connect(ctx)
if err != nil {
    log.Fatal(err)
}

// 注册消息处理器
queueService.RegisterHandler("message_forward", func(data interface{}) error {
    // 处理消息
    return nil
})

// 启动消费者
err = queueService.StartConsumer("message_forward_queue", nil)
if err != nil {
    log.Fatal(err)
}
```

### 监控和管理
```go
// 获取消费者数量
count := queueService.GetConsumerCount()

// 获取活跃消费者列表
activeConsumers := queueService.ListActiveConsumers()

// 获取健康状态
healthStatus := queueService.GetHealthStatus()

// 手动重启消费者
err := queueService.RestartConsumer("message_forward_queue")

// 获取详细统计信息
stats := queueService.GetConsumerStats()
```

## 部署和配置

### 配置参数
- `MaxRetries`：最大重试次数（默认：3）
- `ReconnectDelay`：重连延迟（默认：5秒）
- `PrefetchCount`：预取消息数量

### 监控建议
1. 定期检查 `GetHealthStatus()` 返回的状态
2. 监控消费者错误率，超过阈值时告警
3. 监控消费者重启频率，频繁重启可能表示系统问题

## 故障排除

### 常见问题
1. **消费者频繁重启**：检查网络连接稳定性和消息处理逻辑
2. **高错误率**：检查消息格式和处理逻辑
3. **消费者无法启动**：检查队列配置和权限

### 日志关键字
- "消费者已注册"：消费者成功注册
- "消费者重启成功"：消费者重启成功
- "消费者长时间无活动"：可能的性能问题
- "消费者错误率过高"：需要检查处理逻辑

## 总结

通过实施增强型消费者管理，我们解决了以下核心问题：
1. ✅ 消费者自动重启和故障恢复
2. ✅ 连接重连后消费者自动恢复
3. ✅ 完整的消费者状态跟踪和监控
4. ✅ 增强的健康检查和错误处理
5. ✅ 保持向后兼容性

这些改进确保了消息队列系统的高可用性和稳定性，大大减少了需要手动重启应用的情况。
